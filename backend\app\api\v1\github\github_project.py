"""
GitHub项目API处理器
"""
from typing import Dict, Any, Optional, List
import structlog
from tornado.web import HTT<PERSON><PERSON>rror

from app.api.base import BaseHandler
from app.schemas.article.article_collect import UserArticleCollectCreate
from app.schemas.github.github_project import GitHubProjectCreate, GitHubProject
from app.schemas.github.github_project_collect import UserProjectCollectCreate
from app.services.article.article_service import ArticleService
from app.services.github.github_project import GitHubProjectService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)


class MixedProjectArticleHandler(BaseHandler):
    """项目+文章混合处理器"""

    @inject
    def initialize(
        self,
        github_project_service: GitHubProjectService = Provide[Container.github_project_service],
        article_service: ArticleService = Provide[Container.article_service]
    ):
        self.github_project_service = github_project_service
        self.article_service = article_service
        super().initialize()

    async def get(self) -> None:
        try:
            # 获取分页参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "20"))
            # 公共参数
            status = self.get_argument("status", None)
            name = self.get_argument("name", None)
            recommend_description = self.get_argument("recommend_description", None)
            search = self.get_argument("search", None)
            is_public_str = self.get_argument("is_public", None)
            is_public_str = None if is_public_str == '' else is_public_str
            # 当前用户
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header) if auth_header else None
            # 推荐标签处理
            recommend_tags_str = self.get_argument("recommend_tags", None)
            recommend_tags = self._parse_recommend_tags(recommend_tags_str)
            # 是否推荐
            need_recommend = self.get_argument("need_recommend", "true").lower() == "true"
            logger.info("need_recommend:: " + str(need_recommend))
            # 获取项目

            projects, total_projects = await self.github_project_service.get_projects_new(
                page=page,
                page_size=page_size,
                status=status,
                name=name,
                recommend_description=recommend_description,
                user_id=user_id,
                recommend_tags=recommend_tags
            )

            # articles, total_articles = await self.article_service.get_articles_new(
            #     page=page,
            #     size=half_size,
            #     status=status,
            #     search=search,
            #     is_public=is_public_str,
            #     user_id=user_id,
            #     recommend_tags=recommend_tags
            # )

            # 合并
            # combined_list = self._combine_results(projects, articles)
            # 获取总数（使用现有方法）
            # combined_total = await self.github_project_service.get_all_count()

            self.success_response({
                "items": projects,
                "total": total_projects,
                "page": page,
                "page_size": page_size
            })

        except Exception as e:
            logger.error("获取混合列表时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")

    def _parse_recommend_tags(self, recommend_tags_str: Optional[str]) -> Optional[List[str]]:
        """解析推荐标签字符串"""
        if not recommend_tags_str:
            return None

        try:
            # 按逗号分割，并去除每个标签的前后空格
            recommend_tags = [tag.strip() for tag in recommend_tags_str.split(",") if tag.strip()]
            return recommend_tags if recommend_tags else None
        except Exception as e:
            logger.warning(f"解析推荐标签失败: {str(e)}, recommend_tags_str: {recommend_tags_str}")
            return None

    def _combine_results(self, projects: List, articles: List) -> List[Dict]:
        """合并项目和文章结果，添加展示属性"""
        import random

        combined_list = []

        # 处理项目数据
        for project in projects:
            project_dict = project.dict()
            project_dict["content_type"] = "project"
            project_dict["read_count"] = random.randint(0, 100)  # 临时随机数
            combined_list.append(project_dict)

        # 处理文章数据
        background_colors = ["#e3ecf0", "#e3e5f5", "#f7f4eb", "#ebf6f0"]
        button_colors = ["#07c4fe", "#f4acff", "#ffbaa0"]

        for article in articles:
            article_dict = article.dict()
            article_dict["content_type"] = "article"
            article_dict["background_color"] = random.choice(background_colors)
            article_dict["button_color"] = random.choice(button_colors)
            combined_list.append(article_dict)

        return combined_list


class GitHubProjectCardGenerateHandler(BaseHandler):
    """GitHub项目卡片生成处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:generate"])
    async def post(self) -> None:
        """生成GitHub项目卡片"""
        try:
            # 从请求体获取项目ID列表
            request_data = self.json_body
            project_ids = request_data.get("project_ids", [])

            if not project_ids:
                raise ValueError("project_ids 列表不能为空")

            # 验证项目ID格式
            if not all(isinstance(pid, str) for pid in project_ids):
                raise ValueError("所有项目ID必须是字符串类型")

            # 调用服务生成卡片
            results = await self.github_project_service.generate_cards(project_ids)

            # 返回结果
            self.success_response(results)

        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message=f"请求数据无效: {str(e)}")
        except Exception as e:
            logger.error("处理生成GitHub项目卡片请求时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class GitHubProjectDownloadHandler(BaseHandler):
    """GitHub项目处理器(禁用)"""
    
    @inject
    def initialize(
        self,
        github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器
        
        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    
    # @require_auth(required=True, permissions=["github:project:download"])
    async def post(self) -> None:
        """下载GitHub项目
        
        请求体:
        {
            "repository_url": "https://github.com/username/repo",
            "name": "项目名称" (可选),
            "description": "项目描述" (可选),
            "tags": ["标签1", "标签2"] (可选)
        }
        """
        try:
            projects_data = self.json_body.get("projects", [])
            if not projects_data:
                raise ValueError("projects 列表不能为空")
            # 验证每个项目数据
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            projects = [GitHubProjectCreate(**project) for project in projects_data]
            ans = await self.github_project_service.download_projects(projects)
            # 返回结果
            self.success_response(str(ans) if ans else None)
        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message="请求数据无效" + str(e))
        except Exception as e:
            logger.error("处理下载GitHub项目请求时发生错误", error=str(e))
            self.write_error(500, error_message="服务器内部错误" + str(e))


class GitHubProjectHandler(BaseHandler):
    """GitHub项目处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:list"])
    async def get(self) -> None:
        """获取GitHub项目列表"""
        try:
            # 获取分页参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            
            # 获取搜索参数
            status = self.get_argument("status", None)
            name = self.get_argument("name", None)
            recommend_description = self.get_argument("recommend_description", None)
            user_id = get_current_user_id(auth_header=self.request.headers.get('Authorization'))
            # need_recommend = str(self.get_argument("need_recommend", "true")).lower() == "true"
            recommend_tags_str = self.get_argument("recommend_tags", None)

            recommend_tags = None
            if recommend_tags_str:
                # 使用逗号分隔的字符串格式，例如："python,java,csharp"
                try:
                    # 按逗号分割，并去除每个标签的前后空格
                    recommend_tags = [tag.strip() for tag in recommend_tags_str.split(",") if tag.strip()]

                    # 将中文标签转换为Unicode编码格式
                    processed_tags = []
                    for tag in recommend_tags:
                        processed_tags.append(tag)
                    # 如果分割后没有有效标签，设置为None
                    if not processed_tags:
                        recommend_tags = None
                    else:
                        recommend_tags = processed_tags

                except Exception as e:
                    logger.warning(f"解析推荐标签失败: {str(e)}, recommend_tags_str: {recommend_tags_str}")
                    recommend_tags = None

            # 调用服务获取项目列表
            logger.info("处理 processed_tags::" + str(recommend_tags))
            result, total = await self.github_project_service.get_projects(
                page=page,
                page_size=page_size,
                status=status,
                name=name,
                recommend_description=recommend_description,  # 传递搜索参数
                user_id=user_id,
                recommend_tags=recommend_tags
            )
            
            # 返回结果
            self.success_response({
                "github_projects": [item.dict() for item in result],
                "total": total,
                # "total": 200,
                "page": page,
                "page_size": page_size
            })

        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(500, error_message="请求参数无效" + str(e))
        except Exception as e:
            logger.error("获取GitHub项目列表时发生错误", error=str(e))
            self.write_error(500, error_message="获取GitHub项目列表时发生错误" + str(e))


class ProjectUserCollectHandler(BaseHandler):
    """用户收藏项目处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service],
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            user_project_collect_service: 用户收藏项目服务
        """
        super().initialize()
        self.github_project_service = github_project_service
        self.article_service = article_service

    @require_auth(required=True)
    async def get(self):
        """获取当前用户的收藏项目列表

        查询参数:
        - page: 页码，默认1
        - page_size: 每页大小，默认10
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            type = self.get_argument("type", "project")
            # 获取分页参数
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "10")

            search = self.get_argument("search", "").strip()
            # if not search:
            #     self.write_error(500, error_message="搜索关键词不能为空")
            #     return

            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 30
            except ValueError:
                page_size = 30

            result=None
            if type == "project":
            # 获取用户收藏列表
                result = await self.github_project_service.get_user_collects(
                    user_id=user_id,
                    search=search,
                    page=page,
                    page_size=page_size,
                )
            if type == "article":
                result = await self.article_service.get_user_collects(
                    user_id=user_id,
                    search=search,
                    page=page,
                    page_size=page_size,
                )

            # 返回结果
            self.success_response({
                "collects": [collect.dict() for collect in result.collects],
                # "collects_article": [collect.dict() for collect in result_article.collects],
                "total": result.total,
                # "total_article": result_article.total,
                "page": result.page,
                "page_size": result.page_size
            })

        except ValueError as e:
            logger.error("获取收藏列表参数错误", error=str(e), user_id=user_id)
            self.write_error(500, error_message=f"请求参数错误: {str(e)}")
        except Exception as e:
            logger.error("获取收藏列表失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"获取收藏列表失败: {str(e)}")

    @require_auth(required=True)
    async def post(self):
        """收藏项目或文章

        请求体:
        {
            "project_id": "项目ID"    // 收藏项目时提供
        }
        或
        {
            "article_id": "文章ID"    // 收藏文章时提供
        }
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 验证请求数据
            if not self.json_body:
                raise ValueError("请求体不能为空")

            collect_data = UserProjectCollectCreate.model_validate(self.json_body)

            # 根据请求参数决定收藏项目还是文章
            if collect_data.project_id:
                # 收藏项目
                result = await self.github_project_service.add_collect(
                    user_id=user_id,
                    collect_data=collect_data
                )
                message = "收藏项目成功"
            elif collect_data.article_id:
                # 收藏文章
                tmp = UserArticleCollectCreate(article_id=collect_data.article_id)
                result = await self.article_service.add_collect(
                    user_id,tmp
                )
                message = "收藏文章成功"
            else:
                raise ValueError("必须提供 project_id 或 article_id")

            # 返回结果
            self.success_response(result.dict(), message=message)

        except ValueError as e:
            logger.error("收藏参数错误", error=str(e), user_id=user_id)
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("收藏失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"收藏失败: {str(e)}")

    @require_auth(required=True)
    async def delete(self):
        """取消收藏项目或文章

        查询参数:
        - project_id: 项目ID（取消收藏项目时提供）
        - article_id: 文章ID（取消收藏文章时提供）
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 获取查询参数
            project_id = self.get_argument("project_id", None)
            article_id = self.get_argument("article_id", None)

            # 验证参数
            if not project_id and not article_id:
                raise ValueError("必须提供 project_id 或 article_id")
            
            if project_id and article_id:
                raise ValueError("project_id 和 article_id 不能同时提供")

            # 根据参数决定取消收藏项目还是文章
            if project_id:
                # 取消收藏项目
                success = await self.github_project_service.remove_collect(
                    user_id=user_id,
                    project_id=project_id
                )
                message = "取消收藏项目成功" if success else "取消收藏项目失败"
            else:
                # 取消收藏文章
                success = await self.article_service.remove_collect(
                    user_id=user_id,
                    article_id=article_id
                )
                message = "取消收藏文章成功" if success else "取消收藏文章失败"

            if success:
                self.success_response({"success": True}, message=message)
            else:
                self.write_error(500, error_message="收藏记录不存在")

        except ValueError as e:
            logger.error("取消收藏参数错误", error=str(e), user_id=user_id)
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("取消收藏失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"取消收藏失败: {str(e)}")