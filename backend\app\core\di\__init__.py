"""
依赖注入容器管理器
"""
from typing import Optional
import structlog
from app.core.di.containers import Container
from sqlalchemy.orm import Session
import asyncio

from app.models.github.github_initializer import GitHubInitializer

logger = structlog.get_logger(__name__)

class ContainerManager:
    """容器管理器
    
    提供全局单例访问和资源生命周期管理
    """
    
    def __init__(self):
        """初始化容器管理器"""
        self._container: Optional[Container] = None
        logger.debug("容器管理器初始化")
    
    @property
    def container(self) -> Container:
        """获取容器实例
        
        如果容器未初始化，则延迟初始化
        
        Returns:
            Container: 容器实例
        """
        if self._container is None:
            from .containers import Container
            self._container = Container()
            logger.debug("容器实例创建")
        return self._container
    
    @container.setter
    def container(self, value: Optional[Container]) -> None:
        """设置容器实例
        
        Args:
            value: 容器实例或None
        """
        self._container = value
        if value is None:
            logger.debug("容器实例已清理")
    
    async def init_resources(self) -> None:
        """初始化资源
        
        初始化容器管理的所有资源，包括：
        1. 数据库连接
        2. 缓存连接
        3. 消息队列连接
        4. 其他外部服务连接
        5. 自动扫描待分析项目(发布机模式)
        """
        try:
            # 确保容器已创建
            container = self.container
            container.check_dependencies()
            container.init_resources()
            await asyncio.sleep(0.0)

            # 初始化数据库
            db = container.database()
            
            # 创建数据库表
            db.create_all()
            logger.info("数据库表创建成功")
            
            # 获取数据库提供者
            session_provider = container.async_session_provider()
            
            # 初始化默认数据
            async with session_provider() as session:
                try:
                    # 下载task启动
                    from app.core.di import container_manager
                    container_manager.container.github_downloader()
                    container_manager.container.github_readme_generate()
                    # 初始化默认权限
                    from app.models.rbac.initializer import RBACInitializer
                    initialize_rbac = RBACInitializer(session)
                    initialize_github = GitHubInitializer(session)
                    await initialize_rbac.initialize()
                    await initialize_github.initialize()

                    # myclass init here start task run forever
                    # 提交事务
                    await session.commit()

                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    raise
            await self._auto_scan_projects_if_publisher()


            # 初始化数据源管理器
            await self._init_data_source_manager()

            logger.info("容器资源初始化成功")
            
        except Exception as e:
            logger.error(
                "容器资源初始化失败",
                error=str(e),
                error_type=type(e).__name__
            )
            raise

    async def _auto_scan_projects_if_publisher(self) -> None:
        """如果是发布机，自动扫描待分析项目"""
        try:
            # 导入配置和服务
            from app.core.config import settings
            from app.services.github.github_project import GitHubProjectService

            # 检查是否为发布机
            if settings.distributed.is_publisher:
                logger.info("检测到发布机模式，开始自动扫描待分析项目...")

                session_provider = self.container.session_provider()
                async_session_provider = self.container.async_session_provider()

                # 创建GitHub项目服务实例，传入必需的依赖
                github_service = GitHubProjectService(
                    session=session_provider,
                    async_session=async_session_provider
                )

                # 设置一次扫描的项目数量，可以根据需要调整
                batch_size = 50

                # 执行自动扫描
                result = await github_service.queue_unanalyzed_projects(batch_size)

                if result["status"] == "success":
                    queued_count = len(result["data"]["queued_projects"])
                    failed_count = len(result["data"]["failed_projects"])

                    logger.info(
                        "自动扫描待分析项目完成",
                        queued_projects=queued_count,
                        failed_projects=failed_count,
                        message=result["message"]
                    )

                    # 如果有失败的项目，记录详细信息
                    if failed_count > 0:
                        for failed_project in result["data"]["failed_projects"]:
                            logger.warning(
                                "项目加入队列失败",
                                project_id=failed_project["id"],
                                error=failed_project["error"]
                            )
                else:
                    logger.error("自动扫描待分析项目失败", error=result["message"])

            else:
                logger.info("当前为分析机模式，跳过自动扫描待分析项目")

        except Exception as e:
            logger.error(
                "自动扫描待分析项目时发生错误",
                error=str(e),
                error_type=type(e).__name__,
                exc_info=e
            )
            # 注意：这里不抛出异常，避免影响其他启动流程


    async def _init_data_source_manager(self) -> None:
        """初始化数据源管理器"""
        try:
            logger.info("开始初始化数据源管理器")

            # 获取数据源管理器实例
            data_source_manager = self.container.data_source_manager()

            # 调用初始化方法
            success = await data_source_manager.initialize()

            if success:
                logger.info("数据源管理器初始化成功")
            else:
                logger.warning("数据源管理器初始化失败，但不影响系统启动")

        except Exception as e:
            logger.error(
                "数据源管理器初始化失败",
                error=str(e),
                error_type=type(e).__name__
            )
            # 数据源管理器初始化失败不应该阻止系统启动
            # 只记录错误，不抛出异常

    async def shutdown_resources(self) -> None:
        """清理资源
        
        清理容器管理的所有资源，包括：
        1. 关闭数据库连接
        2. 关闭缓存连接
        3. 关闭消息队列连接
        4. 关闭其他外部服务连接
        """
        if self.container is None:
            return
            
        try:
            # 清理数据库资源
            if self.container.database():
                db = self.container.database()
                await db.cleanup()
                logger.info("数据库资源清理成功")
            
            # 清理Redis资源
            if self.container.redis_client():
                self.container.redis_client().close()
                await asyncio.sleep(0)  # 让出控制权确保资源被释放
                logger.info("Redis资源清理成功")
            
            if self.container.redis_pool():
                self.container.redis_pool().disconnect()
                await asyncio.sleep(0)
                logger.info("Redis连接池清理成功")

            # 清理数据源管理器
            await self._shutdown_data_source_manager()

            logger.info("容器资源清理完成")
            
        except Exception as e:
            logger.error(
                "容器资源清理失败",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
        finally:
            self.container = None

    async def _shutdown_data_source_manager(self) -> None:
        """清理数据源管理器"""
        try:
            logger.info("开始清理数据源管理器")

            # 获取数据源管理器实例
            data_source_manager = self.container.data_source_manager()

            # 调用关闭方法
            await data_source_manager.shutdown()

            logger.info("数据源管理器清理成功")

        except Exception as e:
            logger.error(
                "数据源管理器清理失败",
                error=str(e),
                error_type=type(e).__name__
            )
            # 清理失败不抛出异常，避免影响其他资源的清理

# 创建全局容器管理器实例
container_manager = ContainerManager()

# 导出公共接口
__all__ = [
    'container_manager',
]
