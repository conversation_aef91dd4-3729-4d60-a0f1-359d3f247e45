"""
GitHub项目服务
"""
import os
import random
from dataclasses import dataclass
from typing import Dict, Optional, List, Tuple, Any, Union
from datetime import datetime
from urllib.parse import urlparse

from app.models.article.article import ArticleModel
from app.models.article.article_collect import UserArticleCollectModel
from app.schemas.article.article import ArticleBase
from app.services.github.github_readme_generater import GitHubReadmeGenerate
from sqlalchemy import text, Integer
import structlog
from sqlalchemy import select, and_, func, delete, update, or_, text, String
from sqlalchemy.exc import IntegrityError
import json
import re
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage
from sqlalchemy.orm import selectinload

from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.exceptions.git import GitHubError
from app.model_converters.github.github_project import GitHubProjectConverter
from app.models.github import UserProjectCollectModel
from app.models.github.github_project import GitHubProjectModel as DBGitHubProject, GitHubProjectModel
from app.models.github.github_project_card import GitHubProjectCardModel
from app.models.statistics import UserFlowLogModel
from app.schemas.github.github_card import GitHubCardCreate, GitHubCard, GitHubCardBase
from app.schemas.github.github_project import GitHubProject, GitHubProjectCreate
from app.schemas.github.github_project_collect import UserProjectCollectResponse, UserProjectCollectList, \
    UserProjectCollectCreate
from app.schemas.github.github_share import GitHubSharedCreate
from app.utils.base64_utils import upload_base64_image
from app.utils.flow_log_utils import record_user_flow_log, FlowLogType, record_system_message
from app.utils.github_api_utils import GitHubApiUtils
from app.utils.status_enum import ProjectStatusEnum

from app.services.github.github_readme_generater import GitHubReadmeGenerate
from app.services.github.github_downloader import GitHubDownloader




logger = structlog.get_logger(__name__)



@dataclass
class CollectedProjectAndCardSearchResult:
    """收藏项目和卡片搜索结果"""
    items: List[Dict[str, Any]]
    total: int
    page: int
    page_size: int

class GitHubProjectService:
    """GitHub项目服务"""

    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider
    ):
        self.session = session
        self.async_session = async_session
        self.github_project_converter = GitHubProjectConverter()
        logger.debug("GitHub项目服务初始化完成")

    async def get_projects(
            self,
            *,
            page: int = 1,
            page_size: int = 10,
            status: Optional[str] = None,
            name: Optional[str] = None,
            recommend_description: Optional[str] = None,
            user_id: Optional[str] = None,
            recommend_tags: Optional[List[str]] = None
    ) -> Tuple[List[GitHubProject], int]:
        """获取GitHub项目列表 - 按时间排序版本

        Args:
            page: 页码，从1开始
            page_size: 每页记录数
            status: 项目状态(精确匹配)
            name: 项目名称(模糊匹配)
            recommend_description: 推荐描述搜索关键字
            user_id: 用户ID，用于检查收藏状态
            recommend_tags: 推荐标签列表

        Returns:
            Tuple[List[GitHubProject], int]: 项目列表和总记录数
        """
        try:
            async with self.async_session() as session:
                # 构建基础查询
                stmt = select(DBGitHubProject)
                conditions = []

                # 构建查询条件
                if status is not None:
                    conditions.append(DBGitHubProject.status == status)
                else:
                    # 默认只显示已发布的项目
                    conditions.append(DBGitHubProject.status == 'published')

                if name is not None:
                    conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))

                if recommend_description:
                    conditions.append(
                        DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
                    )

                # 推荐标签过滤
                if recommend_tags:
                    tag_conditions = []
                    for tag in recommend_tags:
                        tag_condition = text(f"""(
                            tags::text ILIKE '%"{tag}"%'
                        )""")
                        tag_conditions.append(tag_condition)

                    if tag_conditions:
                        combined_tag_condition = or_(*tag_conditions)
                        conditions.append(combined_tag_condition)
                        logger.info(f"应用推荐标签过滤: {recommend_tags}")

                # 应用查询条件
                if conditions:
                    stmt = stmt.where(and_(*conditions))

                # 获取总记录数
                count_stmt = select(func.count()).select_from(stmt.subquery())
                total = await session.scalar(count_stmt)

                # 计算分页
                skip = (page - 1) * page_size

                # 🕒 **按时间排序逻辑**
                if recommend_tags:
                    # 如果有推荐标签，优先显示匹配推荐标签的项目，然后按时间排序
                    stmt = (
                        stmt.order_by(
                            # 第一优先级：推荐标签匹配（匹配的在前）
                            text("CASE WHEN tags::text ILIKE ANY(ARRAY[" +
                                 ",".join([f"'%\"{tag}\"%'" for tag in recommend_tags]) +
                                 "]) THEN 1 ELSE 0 END DESC"),
                            # 第二优先级：按创建时间倒序（最新的在前）
                            DBGitHubProject.created_at.desc(),
                            # 第三优先级：按更新时间倒序
                            DBGitHubProject.updated_at.desc()
                        )
                        .offset(skip)
                        .limit(page_size)
                    )
                else:
                    # 没有推荐标签时，纯按时间排序
                    stmt = (
                        stmt.order_by(
                            # 按创建时间倒序（最新的项目在前面）
                            DBGitHubProject.created_at.desc(),
                            # 如果创建时间相同，按更新时间倒序
                            DBGitHubProject.updated_at.desc()
                        )
                        .offset(skip)
                        .limit(page_size)
                    )

                # 执行查询
                result = await session.execute(stmt)
                db_projects = result.scalars().all()

                # 转换为GitHubProject模型
                projects = [GitHubProject.model_validate(p.__dict__) for p in db_projects]

                # 如果提供了 user_id，批量检查项目的收藏状态
                if user_id:
                    project_ids = [project.id for project in projects]
                    collection_status = await self.is_collected_batch(user_id, project_ids)

                    # 更新每个项目的收藏状态
                    for project in projects:
                        project.is_collected = collection_status[project.id]

                logger.info(f"按时间排序获取项目成功，返回 {len(projects)} 个项目，页码: {page}")
                return projects, total

        except Exception as e:
            logger.error("获取GitHub项目列表失败", error=str(e), exc_info=True)
            return [], 0

    async def get_last_accessed_item(
            self,
            user_id: str
    ) -> Union[Optional[GitHubProject], Optional[ArticleModel]]:
        """获取用户最后一次访问的项目或文章（最新的一条记录）

        Args:
            user_id: 用户ID

        Returns:
            返回用户最后访问的项目(GitHubProject)或文章(ArticleModel)，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                # 查询用户的所有访问日志，按创建时间倒序排列，取最新的一条
                stmt = select(UserFlowLogModel.project_id, UserFlowLogModel.log_type).where(
                    UserFlowLogModel.created_by == user_id
                ).order_by(UserFlowLogModel.created_at.desc()).limit(1)

                result = await session.execute(stmt)
                row = result.first()

                if not row:
                    logger.info(f"用户 {user_id} 没有任何访问记录")
                    return None

                last_accessed_id, log_type = row

                # 根据日志类型获取对应的项目或文章信息
                if log_type == FlowLogType.HISTORY:
                    # 获取项目信息
                    project_stmt = select(DBGitHubProject).where(
                        DBGitHubProject.id == last_accessed_id
                    )
                    project_result = await session.execute(project_stmt)
                    db_project = project_result.scalar_one_or_none()
                    ret = GitHubProject.model_validate(db_project.__dict__) if db_project else None
                    return ret.tags
                else:
                    # 获取文章信息
                    article_stmt = select(ArticleModel).where(
                        ArticleModel.id == last_accessed_id
                    )
                    article_result = await session.execute(article_stmt)
                    db_article = article_result.scalar_one_or_none()
                    ret =  ArticleBase.model_validate(db_article.__dict__) if db_article else None
                    return ret.tags
        except Exception as e:
            logger.error(f"获取用户最后访问的项目/文章时发生错误: {str(e)}")
            return None

    async def _get_user_last_tags(self, user_id: str) -> Optional[List[str]]:
        """
        🔍 获取用户历史记录中最后一个项目或文章的标签

        逻辑：
        1. 先查找最后访问的项目标签
        2. 如果没有项目历史，查找最后访问的文章标签
        3. 都没有则返回 None
        """
        try:
            # 首先尝试获取最后访问的项目标签
            last_project = await self.github_project_service.get_last_accessed_project(user_id)
            if last_project and last_project.tags:
                logger.info(f"用户 {user_id} 最后访问项目的标签: {last_project.tags}")
                return last_project.tags

            # 如果没有项目历史，尝试获取最后访问的文章标签
            last_article = await self.article_service.get_last_accessed_article(user_id)
            if last_article and last_article.tags:
                logger.info(f"用户 {user_id} 最后访问文章的标签: {last_article.tags}")
                return last_article.tags

            logger.info(f"用户 {user_id} 没有历史标签记录")
            return None

        except Exception as e:
            logger.error(f"获取用户历史标签失败: {str(e)}")
            return None

    async def get_projects_new(
            self,
            page: int = 1,
            page_size: int = 10,
            status: Optional[str] = None,
            name: Optional[str] = None,
            recommend_description: Optional[str] = None,
            user_id: Optional[str] = None,
            recommend_tags: Optional[List[str]] = None
    ) -> Tuple[List, int]:
        """
        🆕 新的项目获取方法 - 实现智能推荐逻辑

        推荐逻辑：
        1. 用户已登录：根据历史记录的最后一个标签推荐（*不管这个项目是文章），如果传了推荐标签还是使用传入的推荐标签
        2. 用户未登录 + 有推荐标签：使用传入的推荐标签
        3. 用户未登录 + 无推荐标签：按每一天的星数从高到低排序（27日 星数从高到低，26日 星数从高到低）
        """
        try:
            # 🎯 **推荐标签确定逻辑**
            final_recommend_tags = None
            if recommend_tags:
                final_recommend_tags = recommend_tags
                logger.info(f"使用传入的推荐标签: {final_recommend_tags}")
            elif user_id:
                final_recommend_tags = await self.get_last_accessed_item(user_id)
                logger.info(f"未传入推荐标签 使用用户 {user_id} 的历史标签: {final_recommend_tags}")
            else:
                logger.info("用户未登录且无推荐标签，将按人气排序")

            # 📊 **第一步：计算总数统计**
            async with self.async_session() as session:
                # 计算所有符合条件的项目总数（不包括推荐标签）
                projects_total_all = await self._get_projects_count_normal(
                    session, status, name, recommend_description, None
                )

                # 计算所有符合条件的文章总数（不包括推荐标签）
                articles_total_all = await self._get_articles_count_normal(
                    session, status, name, recommend_description, None
                )

                logger.info(f"总项目数: {projects_total_all}, 总文章数: {articles_total_all}")

            # 🔍 **第二步：计算推荐内容总数**
            projects_total = None
            articles_total = None
            async with self.async_session() as session:
                if final_recommend_tags:
                    # 计算符合推荐标签的项目总数
                    projects_total = await self._get_projects_count_normal(
                        session, status, name, recommend_description, final_recommend_tags
                    )

                    # 计算符合推荐标签的文章总数
                    articles_total = await self._get_articles_count_normal(
                        session, status, name, recommend_description, final_recommend_tags
                    )

                    logger.info(f"推荐项目数: {projects_total}, 推荐文章数: {articles_total}")
                else:
                    projects_total = 0
                    articles_total = 0

            # 🧮 **第三步：智能分页逻辑**
            total_recommend_items = projects_total + articles_total
            async with self.async_session() as session:
                if total_recommend_items == 0:
                    # 没有推荐内容，直接返回普通内容
                    logger.info("没有推荐内容，返回普通内容")
                    return await self._get_page_normal(
                        session, page, page_size, status, name, recommend_description,
                        final_recommend_tags, user_id
                    )
                    # 计算推荐内容能填满多少完整页
                full_recommend_pages = total_recommend_items // page_size

                # 计算推荐内容的剩余数量
                remaining_recommend = total_recommend_items % page_size
                logger.info(f" page :: {page} full_recommend_pages:: {full_recommend_pages}  remaining_recommend :: {remaining_recommend}")
                if page <= full_recommend_pages:
                    # 🎯 **情况1：完整推荐页**
                    logger.info(f"第 {page} 页为完整推荐页")
                    return await self._get_page_with_recommend_only(
                        session, page, page_size, status, name, recommend_description,
                        final_recommend_tags, user_id
                    )
                elif page == full_recommend_pages + 1 and remaining_recommend > 0:
                    # 🎯 **情况2：混合页（推荐内容 + 普通内容）**
                    logger.info(f"第 {page} 页为混合页，推荐内容: {remaining_recommend}，普通内容: {page_size - remaining_recommend}")
                    return await self._get_page_mixed(
                        session, remaining_recommend, page_size - remaining_recommend,
                        status, name, recommend_description, final_recommend_tags, user_id
                    )
                else:
                    # 🎯 **情况3：普通内容页**
                    # 括号中为什么要减去1：： 只要存在推荐，那么必然有一页需要普通内容页的混合去补充，所以情况2会必然占去情况3的第一页 也就是说情况3上来是从第二页开始的
                    normal_page = page - full_recommend_pages # - (1 if remaining_recommend > 0 else 0)
                    logger.info(f"第 {page} 页为普通内容页，实际普通页: {normal_page}")
                    return await self._get_page_normal(
                        session, normal_page, page_size, status, name, recommend_description,
                        final_recommend_tags, user_id
                    )

        except Exception as e:
            logger.error("获取项目列表失败", error=str(e), exc_info=True)
            return [], 0

    async def _get_projects_count_normal(
            self, session, status: Optional[str], name: Optional[str],
            recommend_description: Optional[str], tags: Optional[List[str]] = None
    ) -> int:
        """获取符合条件的项目总数"""
        try:
            # 构建基础查询
            stmt = select(func.count()).select_from(DBGitHubProject)
            conditions = [DBGitHubProject.status == 'published']

            # 添加过滤条件
            if status:
                conditions.append(DBGitHubProject.status == status)
            if name:
                conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))
            if recommend_description:
                conditions.append(
                    DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
                )

            # 添加标签条件
            if tags:
                tag_conditions = []
                for tag in tags:
                    tag_condition = text(f"""(
                        tags::text ILIKE '%"{tag}"%'
                    )""")

                    # tag_condition = func.jsonb_typeof(ArticleModel.tags) == 'array'
                    tag_conditions.append(tag_condition)

                if tag_conditions:
                    combined_tag_condition = or_(*tag_conditions)
                    conditions.append(combined_tag_condition)

            # 应用查询条件
            if conditions:
                stmt = stmt.where(and_(*conditions))

            result = await session.execute(stmt)
            return result.scalar() or 0

        except Exception as e:
            logger.error(f"获取项目总数失败: {str(e)}")
            return 0

    async def _get_articles_count_normal(
            self, session, status: Optional[str], name: Optional[str],
            recommend_description: Optional[str], tags: Optional[List[str]] = None
    ) -> int:
        """获取符合条件的文章总数"""
        try:
            # 构建基础查询
            stmt = select(func.count()).select_from(ArticleModel)
            conditions = [
                ArticleModel.status == 'published',
                ArticleModel.is_public == True
            ]

            # 添加过滤条件
            if status:
                conditions.append(ArticleModel.status == status)
            if name:
                conditions.append(
                    or_(
                        ArticleModel.title.ilike(f"%{name}%"),
                        ArticleModel.content.ilike(f"%{name}%")
                    )
                )
            if recommend_description:
                conditions.append(
                    ArticleModel.description.ilike(f"%{recommend_description}%")
                )

            # 添加标签条件
            if tags:
                tag_conditions = []
                for tag in tags:
                    tag_condition = text(f"""(
                                     tags::text ILIKE '%"{tag}"%'
                                 )""")
                    # tag_condition = func.jsonb_typeof(ArticleModel.tags) == 'array'
                    tag_conditions.append(tag_condition)

                if tag_conditions:
                    combined_tag_condition = or_(*tag_conditions)
                    conditions.append(combined_tag_condition)

            # 应用查询条件
            if conditions:
                stmt = stmt.where(and_(*conditions))

            result = await session.execute(stmt)
            return result.scalar() or 0

        except Exception as e:
            logger.error(f"获取文章总数失败: {str(e)}")
            return 0

    async def _get_page_with_recommend_only(
            self, session, page: int, page_size: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str],
            recommend_tags: List[str], user_id: Optional[str] = None
    ) -> Tuple[List, int]:
        """获取纯推荐内容页面"""
        try:
            # 计算当前页在推荐内容中的位置
            start = (page - 1) * page_size
            end = start + page_size

            # 获取推荐项目
            recommended_projects = await self._get_recommended_projects(
                session, start, end, status, name, recommend_description, recommend_tags
            )

            # 获取推荐文章
            recommended_articles = await self._get_recommended_articles(
                session, start, end, status, name, recommend_description, recommend_tags
            )

            # 合并并排序
            combined_items = self._combine_and_sort_items(recommended_projects, recommended_articles)

            # 截取当前页需要的内容
            page_items = combined_items[:page_size]

            # 计算总数（推荐内容总数）
            total_recommend_items = await self._get_total_recommend_count(
                session, status, name, recommend_description, recommend_tags
            )

            total_normal_items = await self._get_total_normal_count(
                session, status, name, recommend_description, recommend_tags
            )

            return page_items, total_recommend_items + total_normal_items

        except Exception as e:
            logger.error(f"获取纯推荐页面失败: {str(e)}")
            return [], 0

    async def _get_page_mixed(
            self, session, recommend_count: int, normal_count: int,
            status: Optional[str], name: Optional[str], recommend_description: Optional[str],
            recommend_tags: List[str], user_id: Optional[str] = None
    ) -> Tuple[List, int]:
        """获取混合页面（推荐内容 + 普通内容）"""
        try:
            # 获取剩余的推荐内容
            total_recommend = await self._get_total_recommend_count(
                session, status, name, recommend_description, recommend_tags
            )

            # 计算推荐内容的起始位置（跳过已完整展示的页）
            full_pages = total_recommend // (total_recommend + normal_count)  # 这里需要修正
            recommend_start = full_pages * (total_recommend + normal_count)

            # 获取剩余的推荐项目
            remaining_recommended_projects = await self._get_recommended_projects(
                session, recommend_start, recommend_start + recommend_count,
                status, name, recommend_description, recommend_tags
            )

            # 获取剩余的推荐文章
            remaining_recommended_articles = await self._get_recommended_articles(
                session, recommend_start, recommend_start + recommend_count,
                status, name, recommend_description, recommend_tags
            )

            # 获取普通内容（第一页）
            normal_projects = await self._get_normal_projects(
                session, 1, normal_count, status, name, recommend_description, recommend_tags
            )

            normal_articles = await self._get_normal_articles(
                session, 1, normal_count, status, name, recommend_description, recommend_tags
            )

            # 合并推荐内容和普通内容
            all_items = []

            # 先添加推荐内容
            all_items.extend(
                self._combine_and_sort_items(remaining_recommended_projects, remaining_recommended_articles))
            # 再添加普通内容
            all_items.extend(self._combine_and_sort_items(normal_projects, normal_articles))

            # 按权重排序
            all_items.sort(key=lambda x: (
                x.get('item_type') == 'project',  # 项目优先
                x.get('created_at', datetime.min),  # 然后按创建时间
            ), reverse=True)

            # 计算总数（推荐内容总数 + 普通内容总数）
            total_recommend_items = await self._get_total_recommend_count(
                session, status, name, recommend_description, recommend_tags
            )
            total_normal_items = await self._get_total_normal_count(
                session, status, name, recommend_description, recommend_tags
            )

            return all_items, total_recommend_items + total_normal_items

        except Exception as e:
            logger.error(f"获取混合页面失败: {str(e)}")
            return [], 0

    async def _get_page_normal(
            self, session, page: int, page_size: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str],
            recommend_tags: List[str], user_id: Optional[str] = None
    ) -> Tuple[List, int]:
        """获取普通内容页面（推荐内容已用完）"""
        try:
            # 获取普通项目（排除已推荐的内容）
            normal_projects = await self._get_normal_projects(
                session, page, page_size, status, name, recommend_description, recommend_tags
            )

            # 获取普通文章（排除已推荐的内容）
            normal_articles = await self._get_normal_articles(
                session, page, page_size, status, name, recommend_description, recommend_tags
            )

            # 合并并排序
            combined_items = self._combine_and_sort_items(normal_projects, normal_articles)


            total_recommend_items = await self._get_total_recommend_count(
                session, status, name, recommend_description, recommend_tags
            )

            # 计算总数（普通内容总数）
            total_normal_items = await self._get_total_normal_count(
                session, status, name, recommend_description, recommend_tags
            )

            return combined_items, total_recommend_items + total_normal_items

        except Exception as e:
            logger.error(f"获取普通页面失败: {str(e)}")
            return [], 0

    async def _get_recommended_projects(
            self, session, start: int, end: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str], tags: List[str]
    ) -> List[Dict]:
        """获取推荐项目"""
        try:
            # 构建查询
            stmt = select(DBGitHubProject)
            conditions = [DBGitHubProject.status == 'published']

            # 添加过滤条件
            if status:
                conditions.append(DBGitHubProject.status == status)
            if name:
                conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))
            if recommend_description:
                conditions.append(
                    DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
                )

            # 添加标签条件
            tag_conditions = []
            for tag in tags:
                tag_condition = text(f"""(
                    tags::text ILIKE '%"{tag}"%'
                )""")
                tag_conditions.append(tag_condition)

            if tag_conditions:
                combined_tag_condition = or_(*tag_conditions)
                conditions.append(combined_tag_condition)

            # 应用查询条件
            if conditions:
                stmt = stmt.where(and_(*conditions))

            # 排序和分页
            stmt = stmt.order_by(
                text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
                DBGitHubProject.created_at.desc()
            ).offset(start).limit(end - start)

            result = await session.execute(stmt)
            db_projects = result.scalars().all()

            # 转换为字典格式
            return [self._convert_project_to_dict(p) for p in db_projects]

        except Exception as e:
            logger.error(f"获取推荐项目失败: {str(e)}")
            return []

    async def _get_recommended_articles(
            self, session, start: int, end: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str], tags: List[str]
    ) -> List[Dict]:
        """获取推荐文章"""
        try:
            # 构建查询
            stmt = select(ArticleModel)
            conditions = [
                ArticleModel.status == 'published',
                ArticleModel.is_public == True
            ]

            # 添加过滤条件
            if status:
                conditions.append(ArticleModel.status == status)
            if name:
                conditions.append(
                    or_(
                        ArticleModel.title.ilike(f"%{name}%"),
                        ArticleModel.content.ilike(f"%{name}%")
                    )
                )
            if recommend_description:
                conditions.append(ArticleModel.description.ilike(f"%{recommend_description}%"))

            # 添加标签条件
            tag_conditions = []
            for tag in tags:
                tag_condition = text(f"""(
                    tags::text ILIKE '%"{tag}"%'
                )""")

                tag_conditions.append(tag_condition)

            if tag_conditions:
                combined_tag_condition = or_(*tag_conditions)
                conditions.append(combined_tag_condition)

            # 应用查询条件
            if conditions:
                stmt = stmt.where(and_(*conditions))

            # 排序和分页
            stmt = stmt.order_by(
                (ArticleModel.read_count + ArticleModel.like_count * 2 + ArticleModel.collect_count * 3).desc(),
                ArticleModel.created_at.desc()
            ).offset(start).limit(end - start)

            result = await session.execute(stmt)
            db_articles = result.scalars().all()

            # 转换为字典格式
            return [self._convert_article_to_dict(a) for a in db_articles]

        except Exception as e:
            logger.error(f"获取推荐文章失败: {str(e)}")
            return []

    async def _get_normal_projects(
            self, session, page: int, page_size: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str], exclude_tags: List[str]
    ) -> List[Dict]:
        """获取普通项目（排除推荐标签）"""
        try:
            # 构建查询
            stmt = select(DBGitHubProject)
            conditions = [DBGitHubProject.status == 'published']

            # 添加过滤条件
            if status:
                conditions.append(DBGitHubProject.status == status)
            if name:
                conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))
            if recommend_description:
                conditions.append(
                    DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
                )

            # 排除推荐标签
            if exclude_tags:
                exclude_conditions = []
                for tag in exclude_tags:
                    exclude_condition = text(f"""(
                        NOT (tags::text ILIKE '%"{tag}"%')
                    )""")
                    exclude_conditions.append(exclude_condition)

                if exclude_conditions:
                    combined_exclude_condition = and_(*exclude_conditions)
                    conditions.append(combined_exclude_condition)

            # 应用查询条件
            if conditions:
                stmt = stmt.where(and_(*conditions))

            # 排序和分页
            stmt = stmt.order_by(
                text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
                DBGitHubProject.created_at.desc()
            ).offset((page - 1) * page_size).limit(page_size)

            result = await session.execute(stmt)
            db_projects = result.scalars().all()

            # 转换为字典格式
            return [self._convert_project_to_dict(p) for p in db_projects]

        except Exception as e:
            logger.error(f"获取普通项目失败: {str(e)}")
            return []

    async def _get_normal_articles(
            self, session, page: int, page_size: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str], exclude_tags: List[str]
    ) -> List[Dict]:
        """获取普通文章（排除推荐标签）"""
        try:
            # 构建查询
            stmt = select(ArticleModel)
            conditions = [
                ArticleModel.status == 'published',
                ArticleModel.is_public == True
            ]

            # 添加过滤条件
            if status:
                conditions.append(ArticleModel.status == status)
            if name:
                conditions.append(
                    or_(
                        ArticleModel.title.ilike(f"%{name}%"),
                        ArticleModel.content.ilike(f"%{name}%")
                    )
                )
            if recommend_description:
                conditions.append(ArticleModel.description.ilike(f"%{recommend_description}%"))

            # 排除推荐标签
            if exclude_tags:
                exclude_conditions = []
                for tag in exclude_tags:
                    exclude_condition = text(f"""(
                        tags::text ILIKE '%"{tag}"%'
                    )""")
                    exclude_conditions.append(exclude_condition)

                if exclude_conditions:
                    # 使用 NOT 来排除
                    combined_exclude_condition = ~or_(*exclude_conditions)
                    conditions.append(combined_exclude_condition)

            # 应用查询条件
            if conditions:
                stmt = stmt.where(and_(*conditions))

            # 排序和分页
            stmt = stmt.order_by(
                (ArticleModel.read_count + ArticleModel.like_count * 2 + ArticleModel.collect_count * 3).desc(),
                ArticleModel.created_at.desc()
            ).offset((page - 1) * page_size).limit(page_size)

            result = await session.execute(stmt)
            db_articles = result.scalars().all()

            # 转换为字典格式
            return [self._convert_article_to_dict(a) for a in db_articles]

        except Exception as e:
            logger.error(f"获取普通文章失败: {str(e)}")
            return []

    def _combine_and_sort_items(self, projects: List[Dict], articles: List[Dict]) -> List[Dict]:
        """合并项目文章并按权重排序"""
        try:
            combined = []

            # 添加项目，标记类型
            for project in projects:
                project['item_type'] = 'project'
                combined.append(project)

            # 添加文章，标记类型
            for article in articles:
                article['item_type'] = 'article'
                combined.append(article)

            # 按权重排序（项目优先，然后按时间）
            combined.sort(key=lambda x: (
                # x.get('item_type') == 'project',  # 项目优先
                x.get('created_at', datetime.min),  # 然后按创建时间
            ), reverse=True)

            return combined

        except Exception as e:
            logger.error(f"合并排序项目文章失败: {str(e)}")
            return []

    def _convert_project_to_dict(self, project) -> Dict:
        """转换项目模型为字典"""
        try:
            return {
                'id': project.id,
                'name': project.name,
                'description': project.description_project,
                'description_recommend': project.description_recommend,
                'repository_url': project.repository_url,
                'stars': project.stars,
                'image_url': project.image_url,
                # 'forks': project.forks,
                # 'language': project.language,
                'tags': project.tags,
                'status': project.status,
                'created_at': project.created_at,
                'updated_at': project.updated_at,
                'content_type': 'project'
            }
        except Exception as e:
            logger.error(f"转换项目为字典失败: {str(e)}")
            return {}

    def _convert_article_to_dict(self, article) -> Dict:
        """转换文章模型为字典"""
        try:
            return {
                'id': article.id,
                'title': article.title,
                #'content': article.content,
                'tags': article.tags,
                'status': article.status,
                'is_public': article.is_public,
                'cover_image': article.cover_image,
                'summary': article.summary,
                'keywords': article.keywords,
                'read_count': article.read_count,
                'like_count': article.like_count,
                'collect_count': article.collect_count,
                'created_at': article.created_at,
                'updated_at': article.updated_at,
                'content_type': 'article'
            }
        except Exception as e:
            logger.error(f"转换文章为字典失败: {str(e)}")
            return {}

    async def _get_total_recommend_count(
            self, session, status: Optional[str], name: Optional[str],
            recommend_description: Optional[str], tags: List[str]
    ) -> int:
        """获取推荐内容总数"""
        try:
            projects_count = await self._get_projects_count_normal(
                session, status, name, recommend_description, tags
            )
            articles_count = await self._get_articles_count_normal(
                session, status, name, recommend_description, tags
            )
            return projects_count + articles_count
        except Exception as e:
            logger.error(f"获取推荐内容总数失败: {str(e)}")
            return 0

    async def _get_total_normal_count(
            self, session, status: Optional[str], name: Optional[str],
            recommend_description: Optional[str], exclude_tags: List[str]
    ) -> int:
        """获取普通内容总数（排除推荐标签）"""
        try:
            projects_count = await self._get_projects_count_normal(
                session, status, name, recommend_description, None
            )
            articles_count = await self._get_articles_count_normal(
                session, status, name, recommend_description, None
            )

            # 减去推荐内容数量
            if exclude_tags:
                recommend_projects_count = await self._get_projects_count_normal(
                    session, status, name, recommend_description, exclude_tags
                )
                recommend_articles_count = await self._get_articles_count_normal(
                    session, status, name, recommend_description, exclude_tags
                )

                projects_count -= recommend_projects_count
                articles_count -= recommend_articles_count

            return max(0, projects_count + articles_count)
        except Exception as e:
            logger.error(f"获取普通内容总数失败: {str(e)}")
            return 0


    # async def get_projects_new(
    #         self,
    #         page: int = 1,
    #         page_size: int = 10,
    #         status: Optional[str] = None,
    #         name: Optional[str] = None,
    #         recommend_description: Optional[str] = None,
    #         user_id: Optional[str] = None,
    #         recommend_tags: Optional[List[str]] = None
    # ) -> Tuple[List, int]:
    #     """
    #
    #     推荐逻辑：
    #     1. 用户已登录：根据历史记录的最后一个标签推荐（*不管这个项目是文章），如果传了推荐标签还是使用传入的推荐标签
    #     2. 用户未登录 + 有推荐标签：使用传入的推荐标签
    #     3. 用户未登录 + 无推荐标签：按每一天的星数从高到低排序（27日 星数从高到低，26日 星数从高到低）
    #     """
    #     try:
    #         # 我们的逻辑是在所有的项目/文章被列出完全前，各自分配一半
    #         # half_size = page_size // 2
    #
    #         if recommend_tags:
    #             final_recommend_tags = recommend_tags
    #             logger.info(f"使用传入的推荐标签: {final_recommend_tags}")
    #         elif user_id:
    #             final_recommend_tags = await self._get_user_last_tags(user_id)
    #             logger.info(f"未传入推荐标签 使用用户 {user_id} 的历史标签: {final_recommend_tags}")
    #         # 由于这里返回X个结果的时候  要先
    #         async with self.github_project_service.async_session() as session:
    #             # 在这里根据页数查询所有除了推荐tag以外的 正常情况下的的 project/articles数目
    #             projects_total_all = None
    #             articles_total_all = None
    #             pass
    #
    #
    #         async with self.github_project_service.async_session() as session:
    #             # 在这里根据页数查询包括推荐tag的，符合条件的所有project数目
    #             projects_total = None
    #             # 在这里根据页数查询包括推荐tag的，符合条件的所有article数目
    #             articles_total = None
    #             pass
    #         # 由于要保证每一页返回的数据必须是pagesize，
    #         # 在这里判断+计算：如果有一方（article，project）在没有可推荐的时候 的偏移量
    #         # 跟所有article，project都被消耗完成的时候从另一方多拿取后 导致的额外的偏移量
    #
    #
    #         # 由于要保证每一页返回的数据必须是20，所以当如果有一个项目/文章先被消耗完的时候，
    #         # 要根据projects_total，articles_total算出推荐完毕后的补偿，如果需要补偿 当页
    #         # 的page_size就会多出一页来（最后剩的推荐+新的一页），之后都是正常走未推荐的逻辑，
    #         # 因为我们知道推荐的总数目，我们自然就知道该返回剩下的还是从推荐拿
    #
    #         # 伪代码：if 没有推荐完 正常返回（如果文章推荐完了项目没推荐完就当页项目推荐的更多）
    #         # if 推荐完了 就往后补未推荐的tag 中间推荐完的那一页我们就让他补出一页（假设有53个项目+文章 每页十个
    #         # 符合要求的文章跟项目有27个 那么第三页就有7+10个项目因为我们不能让一页里没有几个项目），然后剩下的没有tag符合的项目正常分页
    #         async with self.github_project_service.async_session() as session:
    #             # 在这里根据页数查询包括推荐tag的，符合条件的所有project数目
    #             projects_total = None
    #             # 在这里根据页数查询包括推荐tag的，符合条件的所有article数目
    #             articles_total = None
    #             pass
    #
    #
    #     except Exception as e:
    #         logger.error("获取项目列表失败", error=str(e), exc_info=True)
    #         return [], 0

    async def get_all_count(self) -> int:
        """获取所有已发布项目和文章的总数

        Returns:
            int: 已发布项目和文章的总数和
        """
        try:
            async with self.async_session() as session:
                # 获取已发布项目总数
                project_count_stmt = select(func.count()).select_from(
                    select(DBGitHubProject).where(
                        DBGitHubProject.status == 'published'
                    )
                )
                project_total = await session.scalar(project_count_stmt) or 0

                # 获取已发布文章总数
                article_count_stmt = select(func.count()).select_from(
                    select(ArticleModel).where(
                        and_(
                            ArticleModel.status == 'published',
                            ArticleModel.is_public == True
                        )
                    )
                )
                article_total = await session.scalar(article_count_stmt) or 0

                # 返回总和
                combined_total = project_total + article_total
                logger.info(f"已发布项目总数: {project_total}, 已发布文章总数: {article_total}, 合计: {combined_total}")
                return combined_total

        except Exception as e:
            logger.error("获取项目和文章总数失败", error=str(e))
            return 0

    async def search_collected_projects_and_cards(
            self,
            user_id: str,
            query: str,
            page: int = 1,
            page_size: int = 10
    ) -> CollectedProjectAndCardSearchResult:
        """搜索用户收藏的项目和卡片内容

        Args:
            user_id: 用户ID
            query: 搜索关键词（可搜索项目名称和卡片内容）
            page: 页码
            page_size: 每页大小

        Returns:
            CollectedProjectAndCardSearchResult: 搜索结果
        """
        try:
            async with self.async_session() as session:
                # 1. 首先获取用户收藏的项目ID列表
                collect_query = select(UserProjectCollectModel.project_id).where(
                    UserProjectCollectModel.user_id == user_id
                )
                collect_result = await session.execute(collect_query)
                collected_project_ids = [row[0] for row in collect_result.fetchall()]

                if not collected_project_ids:
                    # 用户没有收藏任何项目
                    return CollectedProjectAndCardSearchResult(
                        items=[],
                        total=0,
                        page=page,
                        page_size=page_size
                    )

                # 2. 搜索项目名称匹配的项目
                project_search_condition = GitHubProjectModel.name.ilike(f"%{query}%")
                project_query = select(GitHubProjectModel).where(
                    and_(
                        GitHubProjectModel.id.in_(collected_project_ids),
                        project_search_condition
                    )
                )

                project_result = await session.execute(project_query)
                matching_projects = project_result.scalars().all()

                # 3. 搜索卡片内容匹配的卡片
                card_search_condition = or_(
                    GitHubProjectCardModel.title.ilike(f"%{query}%"),
                    GitHubProjectCardModel.content.ilike(f"%{query}%")
                )

                card_query = select(GitHubProjectCardModel).where(
                    and_(
                        GitHubProjectCardModel.project_id.in_(collected_project_ids),
                        card_search_condition
                    )
                )

                card_result = await session.execute(card_query)
                matching_cards = card_result.scalars().all()

                # 4. 合并结果并去重
                all_items = []

                # 添加匹配的项目
                for project in matching_projects:
                    project_data = {
                        "type": "project",
                        "id": project.id,
                        "name": project.name,
                        "description_project": project.description_project,
                        "description_recommend": project.description_recommend,
                        "repository_url": project.repository_url,
                        "stars": project.stars,
                        "language": project.language,
                        "status": project.status,
                        "image_url": project.image_url,
                        "created_at": project.created_at,
                        "updated_at": project.updated_at
                    }
                    all_items.append(project_data)

                # 添加匹配的卡片
                for card in matching_cards:
                    card_data = {
                        "type": "card",
                        "id": card.id,
                        "project_id": card.project_id,
                        "title": card.title,
                        "content": card.content,
                        "like": card.like,
                        "dislike": card.dislike,
                        "collect": card.collect,
                        "sort_order": card.sort_order,
                        "created_at": card.created_at,
                        "updated_at": card.updated_at
                    }

                    # 获取卡片所属的项目信息
                    project_query = select(GitHubProjectModel).where(
                        GitHubProjectModel.id == card.project_id
                    )
                    project_result = await session.execute(project_query)
                    project = project_result.scalar_one_or_none()

                    if project:
                        card_data["project"] = {
                            "id": project.id,
                            "name": project.name,
                            "description_project": project.description_project,
                            "description_recommend": project.description_recommend,
                            "repository_url": project.repository_url,
                            "stars": project.stars,
                            # "language": project.language,
                            "status": project.status
                        }

                    all_items.append(card_data)

                # 5. 按创建时间倒序排列
                all_items.sort(key=lambda x: x["created_at"], reverse=True)

                # 6. 分页处理
                total = len(all_items)
                offset = (page - 1) * page_size
                paginated_items = all_items[offset:offset + page_size]

                return CollectedProjectAndCardSearchResult(
                    items=paginated_items,
                    total=total,
                    page=page,
                    page_size=page_size
                )

        except Exception as e:
            logger.error("搜索收藏项目和卡片失败", error=str(e), user_id=user_id, query=query)
            raise

    async def subscribe_to_project(self, project_id: str, user_id: str) -> bool:
        """
        订阅项目分析完成通知

        Args:
            project_id: 项目ID
            user_id: 用户ID

        Returns:
            bool: 订阅是否成功
        """
        try:
            async with self.async_session() as session:
                from app.models.github.github_project_subscription import GitHubProjectSubscriptionModel
                from sqlalchemy import select

                # 检查是否已经订阅
                existing_stmt = select(GitHubProjectSubscriptionModel).where(
                    GitHubProjectSubscriptionModel.project_id == project_id,
                    GitHubProjectSubscriptionModel.created_by == user_id
                )
                result = await session.execute(existing_stmt)
                existing_subscription = result.scalar_one_or_none()

                if existing_subscription:
                    logger.info("用户已订阅该项目", project_id=project_id, user_id=user_id)
                    return True

                # 创建新订阅
                subscription = GitHubProjectSubscriptionModel(
                    project_id=project_id,
                    created_by=user_id,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )

                session.add(subscription)
                await session.commit()

                logger.info("创建项目订阅成功", project_id=project_id, created_by=user_id)
                return True

        except Exception as e:
            logger.error("订阅项目失败", project_id=project_id, user_id=user_id, error=str(e))
            return False



    async def get_project_by_url(self, repository_url: str) -> Optional[GitHubProject]:
        """根据仓库URL获取项目信息

        Args:
            repository_url: 仓库URL

        Returns:
            Optional[GitHubProject]: 项目信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                # 生成多种可能的URL格式进行查询
                possible_urls = self._generate_possible_urls_for_db_query(repository_url)

                # 在数据库中查询每个可能的URL
                for possible_url in possible_urls:
                    stmt = select(DBGitHubProject).where(DBGitHubProject.repository_url == possible_url)
                    result = await session.execute(stmt)
                    db_project = result.scalar_one_or_none()

                    if db_project:
                        return GitHubProject.model_validate(db_project.__dict__)

                return None

        except Exception as e:
            logger.error(f"根据URL获取项目信息时出错: {str(e)}")
            return None

    def _generate_possible_urls_for_db_query(self, url: str) -> List[str]:
        """生成可能的仓库URL匹配形式（用于数据库查询）

        生成多种可能的URL形式进行数据库查询，以提高匹配成功率

        Args:
            url: 原始URL

        Returns:
            List[str]: 可能的URL形式列表
        """
        possible_urls = []

        try:
            # 保存原始版本（仅去除空格）
            original_url = url.strip()
            possible_urls.append(original_url)

            # 转为小写版本进行后续处理
            url = original_url.lower()

            # 解析原始URL和小写URL
            original_parsed_url = urlparse(original_url)
            parsed_url = urlparse(url)

            # 提取域名和路径
            domain = parsed_url.netloc
            path = parsed_url.path

            # 移除结尾斜杠
            path = path.rstrip("/")

            # 移除.git后缀
            if path.endswith(".git"):
                path = path[:-4]

            # 版本1: 小写URL (原始URL已在前面添加)

            # 版本2: 带协议的完整域名+路径
            possible_urls.append(f"{parsed_url.scheme}://{domain}{path}")

            # 版本3: 不带协议的域名+路径
            possible_urls.append(f"{domain}{path}")

            # 版本4: 去掉www前缀
            if domain.startswith("www."):
                domain_no_www = domain[4:]
                possible_urls.append(f"{parsed_url.scheme}://{domain_no_www}{path}")
                possible_urls.append(f"{domain_no_www}{path}")

            # 版本5: 添加www前缀
            if not domain.startswith("www."):
                domain_with_www = f"www.{domain}"
                possible_urls.append(f"{parsed_url.scheme}://{domain_with_www}{path}")
                possible_urls.append(f"{domain_with_www}{path}")

            # 如果是GitHub，使用特殊格式
            if "github.com" in domain:
                # 小写路径的处理
                path_parts = [p for p in path.split("/") if p]
                if len(path_parts) >= 2:
                    username = path_parts[0]
                    repo_name = path_parts[1]
                    possible_urls.append(f"github.com/{username}/{repo_name}")
                    possible_urls.append(f"{parsed_url.scheme}://github.com/{username}/{repo_name}")

                # 原始路径的处理（保留大小写）
                original_path = original_parsed_url.path
                original_path = original_path.rstrip("/")
                if original_path.lower().endswith(".git"):
                    original_path = original_path[:-4]

                original_path_parts = [p for p in original_path.split("/") if p]
                if len(original_path_parts) >= 2:
                    original_username = original_path_parts[0]
                    original_repo_name = original_path_parts[1]
                    possible_urls.append(f"github.com/{original_username}/{original_repo_name}")
                    possible_urls.append(
                        f"{original_parsed_url.scheme}://github.com/{original_username}/{original_repo_name}")

                    # 添加原始域名的大小写结合
                    original_domain = original_parsed_url.netloc
                    possible_urls.append(f"{original_domain}{original_path}")
                    possible_urls.append(f"{original_parsed_url.scheme}://{original_domain}{original_path}")

                    # 添加不同对URL组件的大小写组合
                    possible_urls.append(
                        f"{original_parsed_url.scheme}://{original_domain}/{original_username}/{original_repo_name}")
                    possible_urls.append(f"{original_domain}/{original_username}/{original_repo_name}")

            # 去重并返回唯一的URL列表
            return list(set(possible_urls))

        except Exception as e:
            logger.error(f"生成可能的URL匹配形式时出错: {str(e)}")
            return [url]  # 如果出错则返回原始URL

    async def get_user_flow_logs(self, page: int, page_size: int, name: Optional[str] = None) -> Dict[str, Any]:
        """获取用户流程日志列表（排除History类型）

        Args:
            page: 页码
            page_size: 每页大小
            name: 项目名称过滤（可选）

        Returns:
            Dict[str, Any]: 包含日志列表和分页信息的字典
        """
        async with self.async_session() as session:
            try:
                # 构建基础查询，排除History类型的日志
                query = select(UserFlowLogModel).where(
                    UserFlowLogModel.log_type != FlowLogType.HISTORY
                ).order_by(UserFlowLogModel.created_at.desc())

                # 如果提供了项目名称，添加过滤条件
                if name:
                    query = query.where(UserFlowLogModel.project_name.ilike(f"%{name}%"))

                # 获取总数 - 使用相同的查询条件
                count_query = select(func.count()).select_from(query.subquery())
                count_result = await session.execute(count_query)
                total = count_result.scalar()

                # 分页查询
                offset = (page - 1) * page_size
                paginated_query = query.offset(offset).limit(page_size)
                result = await session.execute(paginated_query)
                logs = result.scalars().all()

                # 转换为响应格式
                log_responses = []
                for log in logs:
                    log_responses.append({
                        "id": log.id,
                        "log_type": log.log_type,
                        "project_id": log.project_id,
                        "project_name": log.project_name,
                        "content": log.content,
                        "created_at": log.created_at,
                        "created_by": log.created_by
                    })

                return {
                    "logs": log_responses,
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }

            except Exception as e:
                logger.error("获取用户流程日志失败", error=str(e))
                raise GitHubError(f"获取用户流程日志失败: {str(e)}")


    async def get_user_logs_by_log_type(self, page: int, page_size: int, name: Optional[str] = None,
                                        log_type: Optional[str] = None, user_id: Optional[str] = None) -> Dict[str, Any]:
        """获取用户流程日志列表

        Args:
            page: 页码
            page_size: 每页大小
            name: 项目名称过滤（可选）
            log_type: 日志类型过滤（可选）
            user_id: 用户ID（可选）

        Returns:
            Dict[str, Any]: 包含日志列表和分页信息的字典
        """
        async with self.async_session() as session:
            try:
                # 构建过滤条件
                filter_conditions = []

                # filter
                if name:
                    filter_conditions.append(UserFlowLogModel.project_name.ilike(f"%{name}%"))

                # 如果提供了日志类型，添加过滤条件
                if log_type:
                    filter_conditions.append(UserFlowLogModel.log_type == log_type)

                if user_id:
                    filter_conditions.append(UserFlowLogModel.created_by == user_id)

                # 获取最近7天有数据的记录
                # query = (
                #     select(
                #         UserFlowLogModel,
                #         DBGitHubProject,
                #         func.date(UserFlowLogModel.created_at).label('log_date')
                #     )
                #         .join(DBGitHubProject, UserFlowLogModel.project_id == DBGitHubProject.id)
                #         .where(and_(*filter_conditions) if filter_conditions else True)
                #         .order_by(func.date(UserFlowLogModel.created_at).desc())
                #         .distinct(func.date(UserFlowLogModel.created_at))
                # )
                query = (
                    select(
                        UserFlowLogModel,
                        DBGitHubProject
                    )
                        .join(DBGitHubProject, UserFlowLogModel.project_id == DBGitHubProject.id)
                        .where(and_(*filter_conditions) if filter_conditions else True)
                        .order_by(UserFlowLogModel.created_at.desc())  # 按时间倒序排列
                )


                result = await session.execute(query)
                all_logs = result.all()

                # （已删除）按日期分组并只保留最近7个不同的日期
                unique_dates = set()
                filtered_logs = []
                for log, project in all_logs:
                    # if log_date not in unique_dates and len(unique_dates) < 7:
                    # unique_dates.add(log_date)
                    filtered_logs.append((log, project))

                # 计算总数
                total = len(filtered_logs)

                # 手动分页
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_logs = filtered_logs[start_idx:end_idx]

                # 转换为响应格式
                log_responses = []

                for log, project in paginated_logs:
                    project_info = GitHubProject.model_validate(project.__dict__).model_dump() if project else None

                    # 如果提供了 user_id，检查项目是否被该用户收藏
                    project_info["content_type"] = "project"
                    if user_id and project:
                        project_info["is_collected"] = await self.is_collected(user_id, project.id)
                    else:
                        project_info["is_collected"] = False

                    log_responses.append({
                        "id": log.id,
                        "log_type": log.log_type,
                        "project_id": log.project_id,
                        "project_name": log.project_name,
                        "project": project_info,
                        "content_type": "project",
                        "content": log.content,
                        "created_at": log.created_at,
                        "created_by": log.created_by,
                    })

                return {
                    "logs": log_responses,
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "content_type": "project"
                }

            except Exception as e:
                logger.error("获取用户流程日志失败", error=str(e))
                raise GitHubError(f"获取用户流程日志失败: {str(e)}")

    async def get_history_logs(self, page: int, page_size: int, name: Optional[str] = None,
                               user_id: Optional[str] = None) -> Dict[str, Any]:
        """获取历史日志列表（包括项目历史和文章历史）

        Args:
            page: 页码
            page_size: 每页大小
            name: 项目/文章名称过滤（可选）
            user_id: 用户ID（可选）

        Returns:
            Dict[str, Any]: 包含分开的项目和文章日志列表及分页信息的字典
        """
        async with self.async_session() as session:
            try:
                # 导入文章模型
                from app.models.article.article import ArticleModel

                # 构建项目历史查询的过滤条件
                project_filter_conditions = [UserFlowLogModel.log_type == FlowLogType.HISTORY]
                if name:
                    project_filter_conditions.append(UserFlowLogModel.project_name.ilike(f"%{name}%"))

                # 构建文章历史查询的过滤条件
                article_filter_conditions = [UserFlowLogModel.log_type == FlowLogType.ARTICLE_HISTORY]
                if name:
                    article_filter_conditions.append(UserFlowLogModel.project_name.ilike(f"%{name}%"))

                # 查询项目历史日志
                project_query = (
                    select(
                        UserFlowLogModel,
                        DBGitHubProject,
                        func.date(UserFlowLogModel.created_at).label('log_date')
                    )
                        .join(DBGitHubProject, UserFlowLogModel.project_id == DBGitHubProject.id)
                        .where(and_(*project_filter_conditions))
                        .order_by(func.date(UserFlowLogModel.created_at).desc())
                        .distinct(func.date(UserFlowLogModel.created_at))
                )

                # 查询文章历史日志
                article_query = (
                    select(
                        UserFlowLogModel,
                        ArticleModel,
                        func.date(UserFlowLogModel.created_at).label('log_date')
                    )
                        .join(ArticleModel, UserFlowLogModel.project_id == ArticleModel.id)
                        .where(and_(*article_filter_conditions))
                        .order_by(func.date(UserFlowLogModel.created_at).desc())
                        .distinct(func.date(UserFlowLogModel.created_at))
                )

                # 执行查询
                project_result = await session.execute(project_query)
                article_result = await session.execute(article_query)

                project_logs = project_result.all()
                article_logs = article_result.all()

                # 处理项目历史日志
                project_log_responses = []
                project_unique_dates = set()

                for log, project, log_date in project_logs:
                    if log_date not in project_unique_dates and len(project_unique_dates) < 7:
                        project_unique_dates.add(log_date)

                        project_info = GitHubProject.model_validate(project.__dict__).model_dump() if project else None

                        # 如果提供了 user_id，检查项目是否被该用户收藏
                        if user_id and project:
                            project_info["is_collected"] = await self.is_collected(user_id, project.id)
                        else:
                            project_info["is_collected"] = False

                        project_log_responses.append({
                            "id": log.id,
                            "log_type": log.log_type,
                            "project_id": log.project_id,
                            "project_name": log.project_name,
                            "project": project_info,
                            "content": log.content,
                            "created_at": log.created_at,
                            "created_by": log.created_by
                        })

                # 处理文章历史日志
                article_log_responses = []
                article_unique_dates = set()

                for log, article, log_date in article_logs:
                    if log_date not in article_unique_dates and len(article_unique_dates) < 7:
                        article_unique_dates.add(log_date)

                        article_info = {
                            "id": article.id,
                            "title": article.title,
                            "summary": article.summary,
                            "status": article.status,
                            "read_count": article.read_count,
                            "like_count": article.like_count,
                            "collect_count": article.collect_count,
                            "tags": article.tags,
                            "cover_image": article.cover_image,
                            "is_public": article.is_public,
                            "created_at": article.created_at,
                            "updated_at": article.updated_at
                        } if article else None

                        article_log_responses.append({
                            "id": log.id,
                            "log_type": log.log_type,
                            "project_id": log.project_id,
                            "project_name": log.project_name,
                            "article": article_info,
                            "content": log.content,
                            "created_at": log.created_at,
                            "created_by": log.created_by
                        })

                # 合并进行分页计算
                all_logs_for_pagination = []

                # 添加项目日志到总列表
                for log_item in project_log_responses:
                    all_logs_for_pagination.append(log_item)

                # 添加文章日志到总列表
                for log_item in article_log_responses:
                    all_logs_for_pagination.append(log_item)

                # 按创建时间降序排序
                all_logs_for_pagination.sort(key=lambda x: x["created_at"], reverse=True)

                # 计算总数
                total = len(all_logs_for_pagination)

                # 手动分页
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_logs = all_logs_for_pagination[start_idx:end_idx]

                # 将分页后的结果重新分离为项目和文章
                paginated_project_logs = [item for item in paginated_logs if item["log_type"] == FlowLogType.HISTORY]
                paginated_article_logs = [item for item in paginated_logs if
                                          item["log_type"] == FlowLogType.ARTICLE_HISTORY]

                return {
                    "logs": paginated_project_logs,  # 项目历史日志
                    "logs_article": paginated_article_logs,  # 文章历史日志
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }

            except Exception as e:
                logger.error("获取历史日志失败", error=str(e))
                raise GitHubError(f"获取历史日志失败: {str(e)}")

    async def add_collect(self, user_id: str, collect_data: UserProjectCollectCreate) -> UserProjectCollectResponse:
        """添加收藏项目

        Args:
            user_id: 用户ID
            collect_data: 收藏数据

        Returns:
            UserProjectCollectResponse: 收藏响应数据

        Raises:
            ValueError: 如果项目不存在或已经收藏
        """
        async with self.async_session() as session:
            # 检查项目是否存在
            project_query = select(GitHubProjectModel).where(
                GitHubProjectModel.id == collect_data.project_id
            )
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()

            if not project:
                raise ValueError(f"项目不存在: {collect_data.project_id}")

            # 收藏+1
            project.collect_count = project.collect_count + 1
            # 检查是否已经收藏
            existing_query = select(UserProjectCollectModel).where(
                and_(
                    UserProjectCollectModel.user_id == user_id,
                    UserProjectCollectModel.project_id == collect_data.project_id
                )
            )
            existing_result = await session.execute(existing_query)
            existing = existing_result.scalar_one_or_none()

            if existing:
                raise ValueError("您已经收藏过这个项目了")

            # 创建收藏记录
            collect_model = UserProjectCollectModel(
                user_id=user_id,
                project_id=collect_data.project_id,
                created_by=user_id
            )

            try:
                session.add(collect_model)
                await session.commit()
                await session.refresh(collect_model)

                # 获取完整的收藏信息（包含项目详情）
                return await self._get_collect_with_project(session, collect_model.id)

            except IntegrityError as e:
                await session.rollback()
                logger.error("添加收藏失败", error=str(e), user_id=user_id, project_id=collect_data.project_id)
                raise ValueError("添加收藏失败，可能项目已被收藏")

    async def remove_collect(self, user_id: str, project_id: str) -> bool:
        """取消收藏项目

        Args:
            user_id: 用户ID
            project_id: 项目ID

        Returns:
            bool: 是否取消成功
        """
        async with self.async_session() as session:
            try:
                async with session.begin():
                    # 1. 检查收藏记录是否存在
                    collect_query = select(UserProjectCollectModel).where(
                        and_(
                            UserProjectCollectModel.user_id == user_id,
                            UserProjectCollectModel.project_id == project_id
                        )
                    )
                    collect_result = await session.execute(collect_query)
                    collect_record = collect_result.scalar_one_or_none()

                    if not collect_record:
                        return False  # 收藏记录不存在

                    # 2. 删除收藏记录
                    delete_query = delete(UserProjectCollectModel).where(
                        and_(
                            UserProjectCollectModel.user_id == user_id,
                            UserProjectCollectModel.project_id == project_id
                        )
                    )
                    await session.execute(delete_query)

                    # 3. 减少项目的收藏数目
                    update_query = update(GitHubProjectModel).where(
                        GitHubProjectModel.id == project_id
                    ).values(
                        collect_count=func.greatest(GitHubProjectModel.collect_count - 1, 0)
                    )
                    await session.execute(update_query)

                    return True

            except Exception as e:
                # 记录错误日志
                logger.error(f"取消收藏失败: user_id={user_id}, project_id={project_id}, error={e}")
                return False

    # async def get_user_collects(
    #         self,
    #         user_id: str,
    #         search: str = None,
    #         page: int = 1,
    #         page_size: int = 10,
    # ) -> UserProjectCollectList:
    #     """获取用户收藏列表
    #
    #     Args:
    #         user_id: 用户ID
    #         page: 页码
    #         page_size: 每页大小
    #
    #     Returns:
    #         UserProjectCollectList: 收藏列表
    #     """
    #     async with self.async_session() as session:
    #         # 构建查询
    #         query = (
    #             select(UserProjectCollectModel)
    #                 .options(selectinload(UserProjectCollectModel.project))
    #                 .where(UserProjectCollectModel.user_id == user_id)
    #                 .order_by(UserProjectCollectModel.created_at.desc())
    #         )
    #
    #         # 获取总数
    #         count_query = select(func.count()).select_from(
    #             select(UserProjectCollectModel)
    #                 .where(UserProjectCollectModel.user_id == user_id)
    #                 .subquery()
    #         )
    #         count_result = await session.execute(count_query)
    #         total = count_result.scalar()
    #
    #         # 分页查询
    #         offset = (page - 1) * page_size
    #         paginated_query = query.offset(offset).limit(page_size)
    #         result = await session.execute(paginated_query)
    #         collects = result.scalars().all()
    #
    #         # 转换为响应格式
    #         collect_responses = []
    #         for collect in collects:
    #             project_data = None
    #             if collect.project:
    #                 project_data = self.github_project_converter.to_schema(collect.project)
    #
    #             collect_responses.append(UserProjectCollectResponse(
    #                 id=collect.id,
    #                 user_id=collect.user_id,
    #                 project_id=collect.project_id,
    #                 created_at=collect.created_at,
    #                 project=project_data
    #             ))
    #
    #         return UserProjectCollectList(
    #             collects=collect_responses,
    #             total=total,
    #             page=page,
    #             page_size=page_size
    #         )

    async def get_user_collects(
            self,
            user_id: str,
            search: str = None,
            page: int = 1,
            page_size: int = 10,
    ) -> UserProjectCollectList:
        """获取用户收藏列表

        Args:
            user_id: 用户ID
            search: 搜索关键词（可搜索项目名称和卡片内容）
            page: 页码
            page_size: 每页大小

        Returns:
            UserProjectCollectList: 收藏列表
        """
        async with self.async_session() as session:
            # 首先获取用户收藏的项目ID列表
            collect_query = select(UserProjectCollectModel.project_id).where(
                UserProjectCollectModel.user_id == user_id
            )
            collect_result = await session.execute(collect_query)
            collected_project_ids = [row[0] for row in collect_result.fetchall()]

            if not collected_project_ids:
                # 用户没有收藏任何项目
                return UserProjectCollectList(
                    collects=[],
                    total=0,
                    page=page,
                    page_size=page_size
                )

            # 构建基础查询
            base_query = (
                select(UserProjectCollectModel)
                    .options(selectinload(UserProjectCollectModel.project))
                    .where(UserProjectCollectModel.user_id == user_id)
            )

            # 如果有搜索关键词，添加搜索条件
            if search and search.strip():
                # 搜索项目名称匹配的项目
                project_search_condition = GitHubProjectModel.name.ilike(f"%{search}%")

                # 搜索卡片内容匹配的卡片
                card_search_condition = or_(
                    GitHubProjectCardModel.title.ilike(f"%{search}%"),
                    GitHubProjectCardModel.content.ilike(f"%{search}%")
                )

                # 获取匹配搜索条件的项目ID
                matching_project_ids = set()

                # 1. 搜索项目名称匹配的项目
                project_query = select(GitHubProjectModel.id).where(
                    and_(
                        GitHubProjectModel.id.in_(collected_project_ids),
                        project_search_condition
                    )
                )
                project_result = await session.execute(project_query)
                matching_project_ids.update([row[0] for row in project_result.fetchall()])

                # 2. 搜索卡片内容匹配的卡片对应的项目
                card_query = select(GitHubProjectCardModel.project_id).where(
                    and_(
                        GitHubProjectCardModel.project_id.in_(collected_project_ids),
                        card_search_condition
                    )
                )
                card_result = await session.execute(card_query)
                matching_project_ids.update([row[0] for row in card_result.fetchall()])

                # 只返回匹配搜索条件的收藏项目
                if matching_project_ids:
                    base_query = base_query.where(
                        UserProjectCollectModel.project_id.in_(list(matching_project_ids))
                    )
                else:
                    # 没有匹配的项目，返回空结果
                    return UserProjectCollectList(
                        collects=[],
                        total=0,
                        page=page,
                        page_size=page_size
                    )

            # 添加排序
            query = base_query.order_by(UserProjectCollectModel.created_at.desc())

            # 获取总数
            count_query = select(func.count()).select_from(
                base_query.subquery()
            )
            count_result = await session.execute(count_query)
            total = count_result.scalar()

            # 分页查询
            offset = (page - 1) * page_size
            paginated_query = query.offset(offset).limit(page_size)
            result = await session.execute(paginated_query)
            collects = result.scalars().all()

            # 转换为响应格式
            collect_responses = []
            for collect in collects:
                project_data = None
                if collect.project:
                    project_data = self.github_project_converter.to_schema(collect.project)

                collect_responses.append(UserProjectCollectResponse(
                    id=collect.id,
                    user_id=collect.user_id,
                    project_id=collect.project_id,
                    created_at=collect.created_at,
                    project=project_data,

                ))

            return UserProjectCollectList(
                collects=collect_responses,
                total=total,
                page=page,
                page_size=page_size
            )

    async def is_collected_batch(self, user_id: str, project_ids: list[str]) -> dict[str, bool]:
        """批量检查用户是否已收藏指定项目列表

        Args:
            user_id: 用户ID
            project_ids: 项目ID列表

        Returns:
            dict[str, bool]: 项目ID到收藏状态的映射
        """
        async with self.async_session() as session:
            query = select(UserProjectCollectModel).where(
                and_(
                    UserProjectCollectModel.user_id == user_id,
                    UserProjectCollectModel.project_id.in_(project_ids)
                )
            )
            result = await session.execute(query)
            collected_projects = result.scalars().all()

            # 创建一个字典，默认所有项目都未收藏
            collection_status = {project_id: False for project_id in project_ids}
            # 更新已收藏的项目状态
            for collect in collected_projects:
                collection_status[collect.project_id] = True

            return collection_status

    async def is_collected(self, user_id: str, project_id: str) -> bool:
        """检查用户是否已收藏指定项目

        Args:
            user_id: 用户ID
            project_id: 项目ID

        Returns:
            bool: 是否已收藏
        """
        async with self.async_session() as session:
            query = select(UserProjectCollectModel).where(
                and_(
                    UserProjectCollectModel.user_id == user_id,
                    UserProjectCollectModel.project_id == project_id
                )
            )
            result = await session.execute(query)
            return result.scalar_one_or_none() is not None

    async def _get_collect_with_project(
            self,
            session,
            collect_id: str
    ) -> UserProjectCollectResponse:
        """获取包含项目详情的收藏信息"""
        query = (
            select(UserProjectCollectModel)
                .options(selectinload(UserProjectCollectModel.project))
                .where(UserProjectCollectModel.id == collect_id)
        )
        result = await session.execute(query)
        collect = result.scalar_one()

        project_data = None
        if collect.project:
            project_data = self.github_project_converter.to_schema(collect.project)

        return UserProjectCollectResponse(
            id=collect.id,
            user_id=collect.user_id,
            project_id=collect.project_id,
            created_at=collect.created_at,
            project=project_data
        )


    async def get_project_status_with_pagination(
            self,
            *,
            page: int = 1,
            page_size: int = 10,
            project_phase: Optional[str] = None,
            created_by: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取项目状态分页列表和统计

        Args:
            page: 页码
            page_size: 每页数量
            project_phase: 按项目阶段筛选（可选）

        Returns:
            Dict[str, Any]: 包含分页数据和状态统计的字典
        """
        try:
            async with self.async_session() as session:
                from sqlalchemy import func, and_

                # 构建基础查询
                stmt = select(DBGitHubProject)
                count_stmt = select(func.count(DBGitHubProject.id))

                # 添加筛选条件
                conditions = []
                if project_phase:
                    conditions.append(DBGitHubProject.project_phase == project_phase)

                # 添加创建者筛选条件
                if created_by:
                    conditions.append(DBGitHubProject.created_by == created_by)

                if conditions:
                    stmt = stmt.where(and_(*conditions))
                    count_stmt = count_stmt.where(and_(*conditions))

                # 获取总记录数
                total = await session.scalar(count_stmt)

                # 计算分页
                skip = (page - 1) * page_size

                # 获取分页数据
                stmt = (
                    stmt.order_by(
                        DBGitHubProject.created_at.desc(),
                        DBGitHubProject.updated_at.desc()
                    )
                        .offset(skip)
                        .limit(page_size)
                )

                result = await session.execute(stmt)
                projects = result.scalars().all()

                # 转换为响应格式
                # 使用 Pydantic 模型自动转换
                # projects = [GitHubProject.model_validate(p.__dict__) for p in projects]
                #
                #
                # return {
                #     "total": total,
                #     "projects": projects,
                #     "page": page,
                #     "page_size": page_size,
                #     "total_pages": (total + page_size - 1) // page_size if total > 0 else 0,
                #     # "status_statistics": status_statistics
                # }

                projects = [GitHubProject.model_validate(p.__dict__).model_dump() for p in projects]

                return {
                    "total": total,
                    "projects": projects,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size if total > 0 else 0,
                }

        except Exception as e:
            logger.error("获取项目状态分页数据失败", error=str(e))
            return {
                "total": 0,
                "projects": [],
                "page": page,
                "page_size": page_size,
                "total_pages": 0,
                "status_statistics": {}
            }


    async def cancel_project(self, project_ids: List[str]) -> Dict[str, Any]:
        """取消项目的下载或生成

        Args:
            project_ids: 项目ID列表

        Returns:
            Dict[str, Any]: 取消结果
        """
        try:
            results = []

            async with self.async_session() as session:
                for project_id in project_ids:
                    try:
                        # 1. 检查项目是否存在
                        stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                        result = await session.execute(stmt)
                        project = result.scalar_one_or_none()

                        if not project:
                            results.append({
                                "project_id": project_id,
                                "success": False,
                                "message": "项目不存在"
                            })
                            continue

                        # 2. 根据项目状态进行不同的取消操作
                        project_phase = project.project_phase
                        success = False
                        message = ""

                        # 更新项目状态为取消中
                        # project.project_phase = ProjectStatusEnum.CANCELLING.value
                        await session.commit()

                        if project_phase == ProjectStatusEnum.NOT_DOWNLOAD.value:
                            # 项目还未下载，直接标记为取消
                            project.project_phase = ProjectStatusEnum.CANCELLED.value
                            cancel_success = await GitHubDownloader.cancel_project_download(int(project_id))
                            await session.commit()
                            success = True
                            message = "项目已取消（未开始下载）"

                        elif project_phase == ProjectStatusEnum.DOWNLOADING.value:
                            # 项目正在下载中，从下载队列取消
                            cancel_success = await GitHubDownloader.cancel_project_download(int(project_id))
                            if cancel_success:
                                project.project_phase = ProjectStatusEnum.CANCELLED.value
                                await session.commit()
                                success = True
                                message = "项目已从下载队列中取消"
                            else:
                                # 如果从队列中取消失败，说明可能正在下载，标记为取消状态
                                # 下载器会在下载完成后检查取消状态
                                success = True
                                message = "项目下载取消请求已发送"

                        elif project_phase == ProjectStatusEnum.download_success.value:
                            # 项目下载成功，可能在生成队列中
                            cancel_success = await GitHubReadmeGenerate.cancel_project_generation(project_id)
                            if cancel_success:
                                project.project_phase = ProjectStatusEnum.CANCELLED.value
                                await session.commit()
                                success = True
                                message = "项目已从生成队列中取消"
                            else:
                                success = False
                                message = "从生成队列中取消失败"

                        elif project_phase == ProjectStatusEnum.Generate.value:
                            # 项目正在生成中，尝试取消正在运行的任务

                            # 先取消发布机
                            publisher_cancel_success = await GitHubReadmeGenerate.cancel_project_generation(project_id)
                            if publisher_cancel_success:

                                project.project_phase = ProjectStatusEnum.CANCELLED.value
                                await session.commit()
                                success = True
                                message += "发布机：正在运行的分析任务已取消 "
                            else:
                                success = False
                                message += "发布机：取消正在运行的分析任务失败 "

                            analyzer_cancel_success = await GitHubReadmeGenerate.cancel_project_generation_analyzer(project_id)
                            if analyzer_cancel_success:
                                message += "分析机：正在运行的分析任务已取消"
                            else:
                                message += "分析机：取消正在运行的分析任务失败"

                        elif project_phase in [ProjectStatusEnum.GenerateSuccess.value,
                                               ProjectStatusEnum.SUCCESS.value]:
                            # 项目已完成，无需取消
                            project.project_phase = project_phase  # 恢复原状态
                            success = False
                            message = "项目已完成，无法取消"

                        elif project_phase in [ProjectStatusEnum.download_failed.value,
                                               ProjectStatusEnum.GenerateFail.value,
                                               ProjectStatusEnum.FAILED.value]:
                            # 项目已失败，直接标记为取消
                            project.project_phase = ProjectStatusEnum.CANCELLED.value
                            await session.commit()
                            success = True
                            message = "失败的项目已标记为取消"

                        elif project_phase == ProjectStatusEnum.CANCELLED.value:
                            # 项目已经是取消状态
                            success = True
                            message = "项目已经是取消状态"

                        else:
                            # 未知状态
                            project.project_phase = project_phase  # 恢复原状态
                            await session.commit()
                            success = False
                            message = f"未知的项目状态: {project_phase}"

                        results.append({
                            "project_id": project_id,
                            "project_name": project.name,
                            "original_phase": project_phase,
                            "success": success,
                            "message": message
                        })

                        # 如果成功 删除项目
                        if success:
                            await session.delete(project)
                        await record_user_flow_log(
                            session=session,
                            log_type=FlowLogType.PROJECT_CANCEL,
                            project_id=project.id,
                            project_name=project.name,
                            content=f"{project.repository_url}",
                            created_by=project.created_by
                        )
                        await record_system_message(
                            session=session,
                            message_type=FlowLogType.PROJECT_CANCEL,
                            message=f"项目{project.name}已取消",
                            created_by=project.created_by
                        )
                        await session.commit()
                    except Exception as e:
                        logger.error(f"取消项目 {project_id} 时发生错误: {str(e)}")
                        results.append({
                            "project_id": project_id,
                            "success": False,
                            "message": f"取消失败: {str(e)}"
                        })

            # 统计结果
            successful_count = sum(1 for r in results if r["success"])
            failed_count = len(results) - successful_count

            return {
                "total_projects": len(project_ids),
                "successful_cancellations": successful_count,
                "failed_cancellations": failed_count,
                "results": results,
                "message": f"成功取消 {successful_count} 个项目，{failed_count} 个项目取消失败"
            }

        except Exception as e:
            logger.error(f"批量取消项目时发生错误: {str(e)}")
            return {
                "total_projects": len(project_ids),
                "successful_cancellations": 0,
                "failed_cancellations": len(project_ids),
                "results": [],
                "error": str(e),
                "message": "批量取消操作失败"
            }

    async def solve_priority(self, project_id: str) -> Dict[str, Any]:
        """处理项目的优先级状态

        Args:
            project_id: 项目ID

        Returns:
            Dict[str, Any]: 包含处理结果的字典
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()
                if not project:
                    raise ValueError("项目不存在")

                logger.info("已经快速优先处理:: " + str(project.project_phase))
                if project.project_phase == ProjectStatusEnum.download_failed.value:
                    raise ValueError("项目已下载失败，无法优先处理")
                if project.project_phase == ProjectStatusEnum.GenerateSuccess.value:
                    raise ValueError("项目已分析过")
                if project.project_phase == ProjectStatusEnum.DOWNLOADING.value:
                    # 你区分不了他是想快速下载还是快速分析
                    raise ValueError("项目下载中")
                if project.project_phase == ProjectStatusEnum.Generate.value:
                    # raise ValueError("项目已在分析中")
                    pass
                # 业务中其实事实上不存在NOT_DOWNLOAD但是仍要处理
                if project.project_phase == ProjectStatusEnum.NOT_DOWNLOAD.value:
                    from app.services.github.github_downloader import GitHubDownloader
                    await GitHubDownloader.add_to_download_queue_with_priority(
                        project.created_by,
                        project.id,
                        project.repository_url
                    )
                # 业务中其实事实上不存在NOT_DOWNLOAD
                if project.project_phase == ProjectStatusEnum.WAIT_DOWNLOAD.value:
                    from app.services.github.github_downloader import GitHubDownloader
                    await GitHubDownloader.add_to_download_queue_with_priority(
                        project.created_by,
                        project.id,
                        project.repository_url
                    )
                # 再处理分析
                if project.project_phase == ProjectStatusEnum.download_success.value:

                    await GitHubReadmeGenerate.add_to_generate_queue_priority(str(project_id))
                    project.project_phase = ProjectStatusEnum.WAIT_GENERATE.value

                project.is_priority = True

                await session.commit()
                # 如果没下载完就优先
                return {
                    "success": True,
                    "project_info": {
                        "id": project.id,
                        "name": project.name,
                        "status": project.status,
                        "project_phase": project.project_phase
                    }
                }

        except Exception as e:
            logger.error("处理项目优先级失败", error=str(e), project_id=project_id)
            return {
                "success": False,
                "error": f"处理失败: {str(e)}"
            }

    async def link_check_solver(self, github_links: List[str]) -> Dict[str, Any]:
        """检查GitHub链接的有效性并返回分类结果

        Args:
            github_links: GitHub仓库链接列表

        Returns:
            Dict[str, Any]: 包含有效链接、无效链接和已存在项目的分类结果
        """
        try:
            valid_links = []
            invalid_links = []
            existing_links = []

            # 首先检查数据库中是否存在这些项目
            async with self.async_session() as session:
                for link in github_links:
                    # 检查数据库中是否已存在该项目
                    stmt = select(DBGitHubProject).where(DBGitHubProject.repository_url == link)
                    result = await session.execute(stmt)
                    existing_project = result.scalar_one_or_none()

                    if existing_project:
                        existing_links.append({
                            "url": link,
                            "project_info": {
                                "id": existing_project.id,
                                "name": existing_project.name,
                                "status": existing_project.status
                            }
                        })
                        continue

                    # 如果项目不存在，添加到待验证列表
                    if link not in [e["url"] for e in existing_links]:
                        try:
                            # 使用新的工具类获取仓库信息
                            repo_info = await GitHubApiUtils.get_repository_info(link)
                            valid_links.append({
                                "url": link,
                                "api_info": repo_info
                            })
                        except GitHubError as e:
                            invalid_links.append({
                                "url": link,
                                "reason": str(e)
                            })
                        except Exception as e:
                            logger.error(f"处理链接时发生错误: {str(e)}", link=link)
                            invalid_links.append({
                                "url": link,
                                "reason": f"处理失败: {str(e)}"
                            })

            return {
                "valid_links": valid_links,  # 有效的GitHub链接
                "invalid_links": invalid_links,  # 无效的链接
                "existing_links": existing_links  # 已存在的项目
            }

        except Exception as e:
            logger.error("链接检查失败", error=str(e))
            return {
                "valid_links": [],
                "invalid_links": [],
                "existing_links": [],
                "error": str(e)
            }

    async def set_image_list(self, project_id: str, image_list: List) -> Optional[Dict]:
        """设置项目的图片列表

        Args:
            project_id: 项目ID
            image_list: 图片列表

        Returns:
            Optional[Dict]: 更新后的项目图片列表信息，失败时返回None
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    logger.error("设置图片列表失败：项目不存在", project_id=project_id)
                    return None

                # 将图片列表转换为JSON字符串存储
                db_project.image_list = json.dumps(image_list)

                # 提交更改
                await session.commit()
                await session.refresh(db_project)

                # 返回更新后的图片列表
                return {
                    "project_id": db_project.id,
                    "image_list": json.loads(db_project.image_list) if isinstance(db_project.image_list,
                                                                                  str) else db_project.image_list
                }

        except Exception as e:
            logger.error("设置图片列表失败", error=str(e), project_id=project_id)
            return None

    async def set_shared_info(self, data: GitHubSharedCreate) -> Optional[Dict[str, Any]]:
        """设置项目分享信息

        Args:
            data: 分享信息数据

        Returns:
            Optional[Dict[str, Any]]: 更新后的项目信息，失败时返回None
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == data.project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    logger.error("设置分享信息失败：项目不存在", project_id=data.project_id)
                    return None
                # 更新分享信息
                if data.shared_pic is not None:
                    pic_url =  upload_base64_image(data.shared_pic)
                    db_project.shared_pic = pic_url
                if data.shared_data is not None:
                    db_project.shared_data = data.shared_data

                # 提交更改
                await session.commit()
                await session.refresh(db_project)

                # same as get
                shared_data = None
                if db_project.shared_data:
                    try:
                        shared_data = json.loads(db_project.shared_data)
                        # 确保shared_data是list类型，如果不是可以转换或处理
                        if not isinstance(shared_data, list):
                            shared_data = [shared_data]  # 或者根据业务逻辑处理
                    except json.JSONDecodeError:
                        # 如果解析失败，可以返回空列表或原始数据，根据业务需求决定
                        shared_data = []  # 或者 shared_data = [db_project.shared_data]

                # 返回更新后的项目信息
                return {
                    "project_id": db_project.id,
                    "shared_pic": db_project.shared_pic,
                    "shared_data": shared_data
                }

        except Exception as e:
            logger.error("设置分享信息失败", error=str(e), project_id=data.project_id)
            return None

    async def get_shared_info(self, project_id: str) -> Optional[Dict[str, Any]]:
        """获取项目分享信息

        Args:
            project_id: 项目ID

        Returns:
            Optional[Dict[str, Any]]: 项目分享信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    return None

                # shared_data 2 JSON
                shared_data = None
                if db_project.shared_data:
                    try:
                        shared_data = json.loads(db_project.shared_data)
                        # 确保shared_data是list类型，如果不是可以转换或处理
                        if not isinstance(shared_data, list):
                            shared_data = [shared_data]  # 或者根据业务逻辑处理
                    except json.JSONDecodeError:
                        # 如果解析失败，可以返回空列表或原始数据，根据业务需求决定
                        shared_data = []  # 或者 shared_data = [db_project.shared_data]

                return {
                    "project_id": db_project.id,
                    "shared_pic": db_project.shared_pic,
                    "shared_data": shared_data,  # 现在确保是list类型
                    "shared_count_link": db_project.shared_count_link,
                    "shared_count_qrcode": db_project.shared_count_qrcode,
                }


        except Exception as e:
            logger.error("获取分享信息失败", error=str(e), project_id=project_id)
            return None

    async def share_project(self, project_id: str, share_type:str) -> Optional[int]:
        """增加项目分享次数

        Args:
            project_id: 项目ID

        Returns:
            Optional[int]: 更新后的分享次数，失败时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                if not db_project:
                    logger.error("增加分享次数失败：项目不存在", project_id=project_id)
                    return None
                # 获取当前分享次数并加1
                if share_type == 'qrcode':
                    current_count = int(db_project.shared_count_qrcode) if db_project.shared_count_qrcode else 0
                    new_count = current_count + 1
                    db_project.shared_count_qrcode = str(new_count)
                if share_type == 'link':
                    current_count = int(db_project.current_count_link) if db_project.current_count_link else 0
                    new_count = current_count + 1
                    db_project.shared_count_link = str(new_count)
                # 提交更改
                await session.commit()
                return new_count

        except Exception as e:
            logger.error("增加分享次数失败", error=str(e), project_id=project_id)
            return None


    async def get_image_list(self, project_id: str) -> Optional[List]:
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                if isinstance(db_project.image_list, str):
                    return json.loads(db_project.image_list)
                else:
                    return db_project.image_list  # 如果已经是列表就直接返回
        except Exception as e:
            logger.error(f"获取项目信息时发生错误: {str(e)}")
            return None

    async def update_project(self, project_id:str ,data: GitHubProject) -> Optional[GitHubProject]:
        """更新GitHub项目
        
        Args:
            data: 更新的项目数据
            
        Returns:
            Optional[GitHubProject]: 更新后的项目，失败时返回None
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                
                if not db_project:
                    logger.error("更新项目失败：项目不存在", project_id=data.id)
                    return None
                
                # 更新项目字段
                if data.get('name'):
                    db_project.name = data.get('name')
                if data.get('description_recommend') is not None:
                    db_project.description_recommend = data.get('description_recommend')
                if data.get('description_project') is not None:
                    db_project.description_project = data.get('description_project')
                if data.get('tags'):
                    db_project.tags = data.get('tags')
                if data.get('status'):
                    db_project.status = data.get('status')
                if data.get('project_phase'):
                    db_project.project_phase = data.get('project_phase')
                if data.get('stars'):
                    db_project.stars = data.get('stars')
                if data.get('local_path'):
                    db_project.local_path = data.get('local_path')
                if data.get('image_url') is not None:
                    db_project.image_url = data.get('image_url')
                if data.get('image_list') is not None:
                    db_project.image_list = data.get('image_list')
                if data.get('repository_url'):
                    db_project.repository_url = data.get('repository_url')
                if data.get('background_color'):
                    db_project.background_color = data.get('background_color')
                if data.get('button_color'):
                    db_project.button_color = data.get('button_color')
                if data.get('icon_url') is not None:
                    db_project.icon_url = data.get('icon_url')
                if data.get('architecture_mermaid') is not None:
                    db_project.architecture_mermaid = data.get('architecture_mermaid')
                if data.get('dependency_mermaid') is not None:
                    db_project.dependency_mermaid = data.get('dependency_mermaid')
                if data.get('shared_count_qrcode') is not None:
                    db_project.shared_count_qrcode = data.get('shared_count_qrcode')
                if data.get('shared_count_link') is not None:
                    db_project.shared_count_link = data.get('shared_count_link')
                if data.get('shared_pic') is not None:
                    db_project.shared_pic = data.get('shared_pic')
                if data.get('shared_data') is not None:
                    db_project.shared_data = data.get('shared_data')
                if data.get('is_priority') is not None:
                    db_project.is_priority = data.get('is_priority')
                if data.get('collect_count') is not None:
                    db_project.collect_count = data.get('collect_count')

                # 更新时间
                # db_project.updated_at = datetime.utcnow()
                
                # 提交更改
                await session.commit()
                await session.refresh(db_project)
                # 返回更新后的项目
                return GitHubProject.model_validate(db_project.__dict__)
                
        except Exception as e:
            logger.error("更新项目失败", error=str(e), project_id=data.id)
            return None

    async def update_project_status(self, project_id: str, status: str) -> Optional[GitHubProject]:
        """只更新项目状态

        Args:
            project_id: 项目ID
            status: 新状态

        Returns:
            Optional[GitHubProject]: 更新后的项目，失败返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    logger.error("更新项目状态失败：项目不存在", project_id=project_id)
                    return None

                # 只更新状态字段
                db_project.status = status

                await session.commit()
                await session.refresh(db_project)

                return GitHubProject.model_validate(db_project.__dict__)

        except Exception as e:
            logger.error("更新项目状态失败", error=str(e), project_id=project_id)
            return None


    async def delete_project(self, project_id: str) -> bool:
        """删除GitHub项目
        
        Args:
            project_id: 项目ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                
                if not db_project:
                    logger.error("删除项目失败：项目不存在", project_id=project_id)
                    return False
                
                # 删除项目
                await session.delete(db_project)
                await session.commit()

                logger.info("项目删除成功", project_id=project_id)
                return True
                
        except Exception as e:
            logger.error("删除项目失败", error=str(e), project_id=project_id)
            return False

    async def generate_cards(self,projects_ids: List[str])-> List[Dict[str, Any]] :
        results = []
        async with self.async_session() as session:
            for project_id in projects_ids:
                try:
                    # 检查项目是否已存在
                    stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                    result = await session.execute(stmt)
                    existing_project = result.scalar_one_or_none()

                    if not existing_project:
                        # 如果项目已存在，返回现有项目
                        logger.info(f"项目不存在: {project_id}")
                        results.append({
                            "success": False,
                            "project": str(project_id),
                            "error": ""
                        })
                        continue

                    await GitHubReadmeGenerate.add_to_generate_queue(str(project_id))

                    existing_project.project_phase = ProjectStatusEnum.WAIT_GENERATE.value

                except Exception as e:
                    logger.error(f"生成项目时发生错误: {str(e)}")
                    results.append({
                        "success": False,
                        "project": None,
                        "error": f"服务器错误: {str(e)}"
                    })
        return results

    async def download_projects(self, projects_data: List[GitHubProjectCreate],user_id: str) -> List[Dict[str, Any]]:
        """批量下载GitHub项目

        Args:
            projects_data: 项目创建参数列表

        Returns:
            List[Dict[str, Any]]: 每个项目的下载结果，包含成功标志、项目信息和错误信息
        """
        results = []
        async with self.async_session() as session:
            for data in projects_data:
                try:
                    # 检查项目是否已存在
                    repository_url = data.repository_url
                    stmt = select(DBGitHubProject).where(DBGitHubProject.repository_url == repository_url)
                    result = await session.execute(stmt)
                    existing_project = result.scalar_one_or_none()

                    # if existing_project and (len(projects_data) == 1):
                    #     raise ValueError("该项目已存在或已经在分析中,请重新搜索")

                    # if existing_project:
                    #     # 如果项目已存在，返回现有项目
                    #     logger.info(f"项目已存在: {data.repository_url}")
                    #     results.append({
                    #         "success": False,
                    #         "project": GitHubProject.model_validate(existing_project.__dict__),
                    #         "error": "项目已存在"
                    #     })
                    #     continue
                    if existing_project:
                        project_status = existing_project.project_phase

                        if project_status in [
                            ProjectStatusEnum.WAIT_DOWNLOAD.value,
                            ProjectStatusEnum.DOWNLOADING.value,
                            ProjectStatusEnum.WAIT_GENERATE.value,
                            ProjectStatusEnum.Generate.value
                        ]:
                            # 项目正在处理中，添加订阅
                            subscription_success = await self.subscribe_to_project(
                                project_id=existing_project.id,
                                user_id=user_id
                            )

                            if subscription_success:
                                if len(projects_data) == 1:
                                    raise ValueError("该项目正在分析中，已为您添加完成通知。分析完成后将通过微信和邮件通知您")
                                else:
                                    results.append({
                                        "success": True,
                                        "project": GitHubProject.model_validate(existing_project.__dict__),
                                        "error": "",
                                        "message": "项目正在分析中，已为您添加完成通知"
                                    })
                                    continue
                            else:
                                if len(projects_data) == 1:
                                    raise ValueError("该项目正在分析中，添加通知失败，请稍后重试")

                        elif project_status in [
                            ProjectStatusEnum.GenerateSuccess.value,
                            ProjectStatusEnum.SUCCESS.value
                        ]:
                            if len(projects_data) == 1:
                                raise ValueError("该项目已分析完成，请直接搜索查看")


                    # 创建新项目记录
                    # background_colors = ["#ffe6e6", "#cef9fd", "#f9e9ff"]
                    background_colors = ["#e3ecf0", "#e3e5f5", "#f7f4eb", "#ebf6f0"]
                    button_colors = ["#07c4fe", "#f4acff", "#ffbaa0"]
                    project_model = GitHubProjectModel(
                        name=urlparse(data.repository_url).path.strip('/').split('/')[-1],
                        repository_url=data.repository_url,
                        background_color=random.choice(background_colors),
                        button_color=random.choice(button_colors),
                        project_phase=ProjectStatusEnum.WAIT_DOWNLOAD.value,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        # 新增创建人赋值
                        created_by=user_id
                    )

                    session.add(project_model)
                    await session.commit()
                    await session.refresh(project_model)

                    # 添加到下载队列
                    from app.services.github.github_downloader import GitHubDownloader
                    await GitHubDownloader.add_to_download_queue(
                        "",
                        project_model.id,
                        data.repository_url
                    )
                    # 添加到预定
                    subscription_success = await self.subscribe_to_project(
                        project_id=project_model.id,
                        user_id=user_id
                    )
                    results.append({
                        "success": True,
                        "project": GitHubProject.model_validate(project_model.__dict__),
                        "error": ""
                    })

                except IntegrityError as e:
                    logger.error(f"数据库完整性错误: {str(e)}")
                    await session.rollback()
                    results.append({
                        "success": False,
                        "project": None,
                        "error": f"数据库错误: {str(e)}"
                    })
                except Exception as e:
                    logger.error(f"下载项目时发生错误: {str(e)}")
                    raise e
                    # results.append({
                    #     "success": False,
                    #     "project": None,
                    #     "error": f"服务器错误: {str(e)}"
                    # })

        return results

    async def get_project(self, project_id: str,user_id: str = None) -> Optional[GitHubProject]:
        """获取项目信息
        
        Args:
            project_id: 项目ID
        Returns:
            Optional[GitHubProject]: 项目信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                
                if not db_project:
                    return None
                
                project =  GitHubProject.model_validate(db_project.__dict__)

                # 如果提供了 user_id，检查收藏状态
                if user_id:
                    # 使用之前优化过的批量查询方法
                    collection_status = await self.is_collected_batch(user_id, [project_id])
                    project.is_collected = collection_status[project_id]
                else:
                    project.is_collected = False

                return project

        except Exception as e:
            logger.error(f"获取项目信息时发生错误: {str(e)}")
            return None

    async def get_last_accessed_project(self, user_id: str) -> Optional[GitHubProject]:
        """获取用户最后一次访问的项目

        Args:
            user_id: 用户ID

        Returns:
            Optional[GitHubProject]: 最后访问的项目信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                # 查询用户的项目访问日志，筛选历史记录类型，按创建时间倒序排列
                stmt = select(UserFlowLogModel.project_id).where(
                    and_(
                        UserFlowLogModel.created_by == user_id,
                        UserFlowLogModel.log_type == FlowLogType.HISTORY
                    )
                ).order_by(UserFlowLogModel.created_at.desc()).limit(1)

                result = await session.execute(stmt)
                last_accessed_project_id = result.scalar_one_or_none()

                if not last_accessed_project_id:
                    return None

                # 获取对应项目信息
                project_stmt = select(DBGitHubProject).where(
                    DBGitHubProject.id == last_accessed_project_id
                )
                project_result = await session.execute(project_stmt)
                db_project = project_result.scalar_one_or_none()

                if db_project:
                    return GitHubProject.model_validate(db_project.__dict__)
                return None

        except Exception as e:
            logger.error(f"获取用户最后访问的项目时发生错误: {str(e)}")
            return None


    async def _get_projects_with_recommendation(
            self, session, page: int, page_size: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str],
            user_id: str, recommend_tags: Optional[List]
    ) -> Tuple[List[GitHubProject], int]:
        """推荐模式获取项目，包含补全逻辑"""

        # 获取推荐标签
        tags_to_recommend = recommend_tags
        last_project = None

        if not tags_to_recommend:
            # 获取用户最后一次访问的项目
            last_project = await self.get_last_accessed_project(user_id)
            if last_project and last_project.tags:
                tags_to_recommend = last_project.tags
                logger.info(f"用户 {user_id} 基于最后访问项目的标签进行推荐: {tags_to_recommend}")

        # 第一步：获取推荐项目
        recommended_projects = []
        recommended_project_ids = set()

        if tags_to_recommend:
            # 计算推荐项目需要的数量（考虑分页）
            recommended_count = page_size
            recommended_projects = await self._get_projects_by_tags(
                session, tags_to_recommend, last_project, recommended_count, page
            )
            recommended_project_ids = {p.id for p in recommended_projects}
            logger.info(f"推荐逻辑返回 {len(recommended_projects)} 个项目")

            # 第二步：如果推荐项目不足一页，用普通逻辑补全（考虑分页）
        final_projects = recommended_projects

        if len(recommended_projects) < page_size:
            needed_count = page_size - len(recommended_projects)
            logger.info(f"推荐项目不足，需要补充 {needed_count} 个项目")

            # 获取普通逻辑的项目，排除已推荐的项目，考虑分页
            supplement_projects = await self._get_projects_normal_exclude_ids_with_pagination(
                session, status, name, recommend_description,
                recommended_project_ids, needed_count, page
            )

            final_projects.extend(supplement_projects)
            logger.info(f"补充了 {len(supplement_projects)} 个项目")

        # 计算分页
        # skip = (page - 1) * page_size
        # paginated_projects = final_projects[skip:skip + page_size]
        paginated_projects = final_projects


        # 批量检查收藏状态
        if user_id and paginated_projects:
            project_ids = [project.id for project in paginated_projects]
            collection_status = await self.is_collected_batch(user_id, project_ids)

            for project in paginated_projects:
                project.is_collected = collection_status[project.id]

        # 获取总数（推荐模式下的总数基于实际获取的项目数）
        total = len(final_projects)
        logger.info(f"推荐模式获取项目成功，返回 {len(paginated_projects)} 个项目，总数: {total}")
        return paginated_projects, total

    async def _get_projects_normal_exclude_ids_with_pagination(
            self, session, status: Optional[str], name: Optional[str],
            recommend_description: Optional[str], exclude_ids: set, limit: int, page: int
    ) -> List[GitHubProject]:
        """获取普通逻辑的项目，排除指定的项目ID，支持分页"""

        # 构建基础查询
        stmt = select(DBGitHubProject)
        conditions = []

        # 普通筛选条件
        if status is not None:
            conditions.append(DBGitHubProject.status == status)

        if name is not None:
            conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))

        if recommend_description:
            conditions.append(
                DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
            )

        # 排除已推荐的项目
        # if exclude_ids:
        #     conditions.append(~DBGitHubProject.id.in_(exclude_ids))

        # 应用查询条件
        if conditions:
            stmt = stmt.where(and_(*conditions))

        # 计算分页偏移量
        skip = (page - 1) * limit

        # 默认排序
        stmt = stmt.order_by(
            func.date(DBGitHubProject.created_at).desc(),
            text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
            DBGitHubProject.created_at.desc(),
            DBGitHubProject.updated_at.desc()
        ).offset(skip).limit(limit)

        result = await session.execute(stmt)
        db_projects = result.scalars().all()

        return [GitHubProject.model_validate(p.__dict__) for p in db_projects]

    async def _get_projects_by_tags(
            self, session, tags_to_recommend: List, last_project, limit: int,page: int = 1
    ) -> List[GitHubProject]:
        """根据标签获取推荐项目"""

        # 构建基础查询
        stmt = select(DBGitHubProject)
        tag_sql_conditions = []

        for tag in tags_to_recommend:
            # 转义单引号，防止 SQL 注入

            # 同时搜索原始中文和 Unicode 编码
            tag_condition = text(f"""(
                tags::text ILIKE '%"{tag}"%'
              
            )""")
            tag_sql_conditions.append(tag_condition)
            # logger.info(f"搜索标签: 原始='{escaped_tag}', Unicode='{escaped_unicode_tag}'")

        conditions = []
        if tag_sql_conditions:
            # 将所有标签条件用 OR 连接
            combined_tag_condition = or_(*tag_sql_conditions)
            conditions.append(combined_tag_condition)

        # 如果有推荐标签，排除用户最后访问的项目本身
        if last_project:
            conditions.append(DBGitHubProject.id != last_project.id)

        # 应用查询条件
        if conditions:
            stmt = stmt.where(and_(*conditions))

        skip = (page - 1) * limit
        stmt = stmt.offset(skip).limit(limit)

        # 推荐模式排序：优先按 star 数排序
        stmt = stmt.order_by(
            text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
            DBGitHubProject.created_at.desc(),
            DBGitHubProject.updated_at.desc()
        ).limit(limit)

        skip = (page - 1) * limit
        stmt = stmt.offset(skip).limit(limit)

        result = await session.execute(stmt)
        db_projects = result.scalars().all()

        return [GitHubProject.model_validate(p.__dict__) for p in db_projects]

    async def _get_projects_normal_exclude_ids(
            self, session, status: Optional[str], name: Optional[str],
            recommend_description: Optional[str], exclude_ids: set, limit: int
    ) -> List[GitHubProject]:
        """获取普通逻辑的项目，排除指定的项目ID"""

        # 构建基础查询
        stmt = select(DBGitHubProject)
        conditions = []

        # 普通筛选条件
        if status is not None:
            conditions.append(DBGitHubProject.status == status)

        if name is not None:
            conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))

        if recommend_description:
            conditions.append(
                DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
            )

        # 排除已推荐的项目
        if exclude_ids:
            conditions.append(~DBGitHubProject.id.in_(exclude_ids))

        # 应用查询条件
        if conditions:
            stmt = stmt.where(and_(*conditions))

        # 默认排序
        stmt = stmt.order_by(
            func.date(DBGitHubProject.created_at).desc(),
            text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
            DBGitHubProject.created_at.desc(),
            DBGitHubProject.updated_at.desc()
        ).limit(limit)

        result = await session.execute(stmt)
        db_projects = result.scalars().all()

        return [GitHubProject.model_validate(p.__dict__) for p in db_projects]

    async def _get_projects_normal(
            self, session, page: int, page_size: int, status: Optional[str],
            name: Optional[str], recommend_description: Optional[str], user_id: Optional[str],
            recommend_tags: Optional[List]
    ) -> Tuple[List[GitHubProject], int]:
        """非推荐模式的原始逻辑，现在支持推荐标签"""

        # 构建基础查询
        stmt = select(DBGitHubProject)
        conditions = []

        # 非推荐模式：使用原来的筛选逻辑
        if status is not None:
            conditions.append(DBGitHubProject.status == status)

        if name is not None:
            conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))

        if recommend_description:
            conditions.append(
                DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
            )

        # 推荐标签过滤 - 使用 LIKE 查询
        if recommend_tags:
            # 构建推荐标签的 LIKE 条件
            recommend_tag_conditions = []
            for tag in recommend_tags:
                from sqlalchemy import text
                tag_condition = text(f"""(
                    tags::text ILIKE '%"{tag}"%' 
                )""")
                recommend_tag_conditions.append(tag_condition)
            if recommend_tag_conditions:
                # 所有推荐标签条件用 OR 连接
                combined_recommend_tag_condition = or_(*recommend_tag_conditions)
                conditions.append(combined_recommend_tag_condition)
                logger.info(f"应用推荐标签过滤: {recommend_tags}")

        # 应用查询条件
        if conditions:
            stmt = stmt.where(and_(*conditions))

        # 获取总记录数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total = await session.scalar(count_stmt)

        # 计算分页
        skip = (page - 1) * page_size

        # 排序逻辑：如果有推荐标签，优先显示匹配推荐标签的项目
        if recommend_tags:
            # 创建推荐标签匹配的排序条件
            from sqlalchemy import text

            # 构建推荐标签匹配的排序条件
            recommend_conditions = []
            for tag in recommend_tags:
                # escaped_tag = tag.replace("'", "''")
                # unicode_tag = tag.encode('unicode_escape').decode('ascii')
                # escaped_unicode_tag = unicode_tag.replace('\\', '\\\\').replace("'", "''")

                recommend_condition = text(f"""(
                    tags::text ILIKE '%"{tag}"%' 
               
                )""")
                recommend_conditions.append(recommend_condition)

            # 如果有推荐标签匹配条件，添加到排序中
            if recommend_conditions:
                from sqlalchemy import text
                combined_recommend_condition = or_(*recommend_conditions)
                stmt = (
                    stmt.order_by(
                        combined_recommend_condition.desc(),  # 推荐标签匹配的项目优先
                        func.date(DBGitHubProject.created_at).desc(),
                        text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
                        DBGitHubProject.created_at.desc(),
                        DBGitHubProject.updated_at.desc()
                    )
                        .offset(skip)
                        .limit(page_size)
                )
            else:
                stmt = (
                    stmt.order_by(
                        func.date(DBGitHubProject.created_at).desc(),
                        text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
                        DBGitHubProject.created_at.desc(),
                        DBGitHubProject.updated_at.desc()
                    )
                        .offset(skip)
                        .limit(page_size)
                )
        else:
            # 默认排序：按创建日期倒序，同一天按 star 数排序
            from sqlalchemy import text
            stmt = (
                stmt.order_by(
                    func.date(DBGitHubProject.created_at).desc(),
                    text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
                    DBGitHubProject.created_at.desc(),
                    DBGitHubProject.updated_at.desc()
                )
                    .offset(skip)
                    .limit(page_size)
            )

        result = await session.execute(stmt)
        db_projects = result.scalars().all()

        projects = [GitHubProject.model_validate(p.__dict__) for p in db_projects]

        # 如果提供了 user_id，批量检查项目的收藏状态
        if user_id:
            project_ids = [project.id for project in projects]
            collection_status = await self.is_collected_batch(user_id, project_ids)

            # 更新每个项目的收藏状态
            for project in projects:
                project.is_collected = collection_status[project.id]

        logger.info(f"普通模式获取项目成功，返回 {len(projects)} 个项目，推荐标签: {recommend_tags}")
        return projects, total


    # async def _get_projects_normal(
    #         self, session, page: int, page_size: int, status: Optional[str],
    #         name: Optional[str], recommend_description: Optional[str], user_id: Optional[str],
    #         recommend_tags: Optional[List]
    # ) -> Tuple[List[GitHubProject], int]:
    #     """非推荐模式的原始逻辑"""
    #
    #     # 构建基础查询
    #     stmt = select(DBGitHubProject)
    #     conditions = []
    #
    #     # 非推荐模式：使用原来的筛选逻辑
    #     if status is not None:
    #         conditions.append(DBGitHubProject.status == status)
    #
    #     if name is not None:
    #         conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))
    #
    #     if recommend_description:
    #         conditions.append(
    #             DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%")
    #         )
    #
    #     # 应用查询条件
    #     if conditions:
    #         stmt = stmt.where(and_(*conditions))
    #
    #     # 获取总记录数
    #     count_stmt = select(func.count()).select_from(stmt.subquery())
    #     total = await session.scalar(count_stmt)
    #
    #     # 计算分页
    #     skip = (page - 1) * page_size
    #
    #     # 默认排序：按创建日期倒序，同一天按 star 数排序
    #     stmt = (
    #         stmt.order_by(
    #             func.date(DBGitHubProject.created_at).desc(),
    #             text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
    #             DBGitHubProject.created_at.desc(),
    #             DBGitHubProject.updated_at.desc()
    #         )
    #             .offset(skip)
    #             .limit(page_size)
    #     )
    #
    #     result = await session.execute(stmt)
    #     db_projects = result.scalars().all()
    #
    #     projects = [GitHubProject.model_validate(p.__dict__) for p in db_projects]
    #
    #     # 如果提供了 user_id，批量检查项目的收藏状态
    #     if user_id:
    #         project_ids = [project.id for project in projects]
    #         collection_status = await self.is_collected_batch(user_id, project_ids)
    #
    #         # 更新每个项目的收藏状态
    #         for project in projects:
    #             project.is_collected = collection_status[project.id]
    #
    #     logger.info(f"普通模式获取项目成功，返回 {len(projects)} 个项目")
    #     return projects, total

    # async def get_projects(
    #         self,
    #         *,
    #         page: int = 1,
    #         page_size: int = 10,
    #         status: Optional[str] = None,
    #         name: Optional[str] = None,
    #         recommend_description: Optional[str] = None,
    #         user_id: Optional[str] = None,
    #         need_recommend:Optional[bool] = False,
    #         recommend_tags:Optional[List] = None
    # ) -> List[GitHubProject]:
    #     """获取GitHub项目列表
    #
    #     Args:
    #         page: 页码
    #         page_size: 每页记录数
    #         status: 项目状态(精确匹配)
    #         name: 项目名称(模糊匹配)
    #         recommend_description: 推荐描述搜索关键字
    #
    #     Returns:
    #         Tuple[List[GitHubProject], int]: 项目列表和总记录数
    #     """
    #     try:
    #         async with self.async_session() as session:
    #             # 构建基础查询
    #             stmt = select(DBGitHubProject)
    #
    #             # 构建查询条件
    #             conditions = []
    #             if need_recommend:
    #                 if recommend_tags:
    #                     conditions.append(DBGitHubProject.tags.overlap(recommend_tags))
    #                 else:
    #                     last_project = await self.get_last_accessed_project(user_id)
    #                     if last_project:
    #                         for tag in last_project.tags:
    #                             conditions.append(DBGitHubProject.tags.contains([tag]))
    #                     else:
    #                         if status is not None:
    #                             conditions.append(DBGitHubProject.status == status)
    #
    #                         if name is not None:
    #                             conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))
    #
    #                         if recommend_description:
    #                             conditions.append(
    #                                 DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%"))
    #             else:
    #                 if status is not None:
    #                     conditions.append(DBGitHubProject.status == status)
    #
    #                 if name is not None:
    #                     conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))
    #
    #                 if recommend_description:
    #                     conditions.append(DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%"))
    #
    #             # 应用查询条件
    #             if conditions:
    #                 stmt = stmt.where(and_(*conditions))
    #
    #             # 获取总记录数
    #             count_stmt = select(func.count()).select_from(stmt.subquery())
    #             total = await session.scalar(count_stmt)
    #
    #             # 计算分页
    #             skip = (page - 1) * page_size
    #             from sqlalchemy import text
    #             # 获取分页数据
    #
    #             stmt = (
    #                 stmt.order_by(
    #                     # 大排序：按创建日期倒序，最新的项目在前面
    #                     func.date(DBGitHubProject.created_at).desc(),
    #                     # 小排序：同一天的项目按star数从高到低
    #                     text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE 0 END DESC"),
    #                     # 如果star数相同，再按创建时间倒序
    #                     DBGitHubProject.created_at.desc(),
    #                     # 最后按更新时间倒序
    #                     DBGitHubProject.updated_at.desc()
    #                 )
    #                     .offset(skip)
    #                     .limit(page_size)
    #             )
    #             #
    #             # stmt = (
    #             #     stmt.order_by(
    #             #         text("CASE WHEN stars ~ '^[0-9]+$' THEN CAST(stars AS INTEGER) ELSE NULL END DESC NULLS LAST"),
    #             #         DBGitHubProject.created_at.desc(),
    #             #         DBGitHubProject.updated_at.desc()
    #             #     )
    #             #         .offset(skip)
    #             #         .limit(page_size)
    #             # )
    #
    #             result = await session.execute(stmt)
    #             db_projects = result.scalars().all()
    #
    #             projects = [GitHubProject.model_validate(p.__dict__) for p in db_projects]
    #
    #             # 如果提供了 user_id，检查每个项目是否被该用户收藏
    #             # 如果提供了 user_id，批量检查项目的收藏状态
    #             if user_id:
    #                 project_ids = [project.id for project in projects]
    #                 collection_status = await self.is_collected_batch(user_id, project_ids)
    #
    #                 # 更新每个项目的收藏状态
    #                 for project in projects:
    #                     project.is_collected = collection_status[project.id]
    #
    #             return projects, total
    #
    #     except Exception as e:
    #         logger.error("获取GitHub项目列表失败", error=str(e))
    #         return [], 0
    
    async def count_projects(self) -> int:
        """获取项目总数
        
        Returns:
            int: 项目总数
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject)
                result = await session.execute(stmt)
                count = len(result.scalars().all())
                return count
        except Exception as e:
            logger.error("获取项目总数失败", error=str(e))
            return 0

    # 卡片删除了让他写
    async def create_card(self, data: GitHubCardCreate) -> Optional[GitHubCard]:
        """创建卡片

        Args:
            data: 卡片创建数据

        Returns:
            GitHubCard: 创建的卡片数据
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否存在
                project = await session.execute(
                    select(GitHubProjectModel).where(GitHubProjectModel.id == data.project_id)
                )
                project = project.scalar_one_or_none()
                if not project:
                    logger.error("项目不存在", project_id=data.project_id)
                    return None

                # 创建卡片
                card = GitHubProjectCardModel(
                    content=data.content,
                    title=data.title,
                    project_id=data.project_id,
                    sort_order=data.sort_order
                )
                session.add(card)
                await session.commit()
                await session.refresh(card)

                return GitHubCard.model_validate(card.__dict__)
        except Exception as e:
            logger.error("创建卡片失败", error=str(e))
            return None

    async def get_project_cards(self,  project_id: Optional[str] = None, page: int = 1, page_size: int = 10, user_id: str = None) -> List[GitHubCard]:
        """获取项目的所有卡片

        Args:
            project_id: 项目ID

        Returns:
            List[GitHubCard]: 卡片列表
        """
        try:
            async with self.async_session() as session:
                # 构建查询
                if project_id and project_id.strip():
                    # 原有的查询逻辑保持不变
                    result = await session.execute(
                        select(GitHubProjectCardModel)
                            .where(GitHubProjectCardModel.project_id == project_id)
                            .order_by(GitHubProjectCardModel.sort_order)
                            .offset((page - 1) * page_size)
                            .limit(page_size)
                    )
                else:
                    # 当project_id为空时，返回所有卡片
                    result = await session.execute(
                        select(GitHubProjectCardModel)
                            .order_by(GitHubProjectCardModel.sort_order)
                            .offset((page - 1) * page_size)
                            .limit(page_size)
                    )

                cards = result.scalars().all()
                if user_id:
                    await record_user_flow_log(
                        session=session,
                        log_type=FlowLogType.HISTORY,
                        project_id=project_id,
                        created_by=user_id
                    )

                ans = [GitHubCard.model_validate(card.__dict__) for card in cards]
                await session.commit()
                return ans
        except Exception as e:
            logger.error("获取项目卡片失败", error=str(e), project_id=project_id)
            return []

    async def get_card(self, card_id: str) -> Optional[GitHubCard]:
        """获取卡片详情

        Args:
            card_id: 卡片ID

        Returns:
            Optional[GitHubCard]: 卡片详情
        """
        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == card_id)
                )
                card = result.scalar_one_or_none()

                if card:
                    return GitHubCard.model_validate(card.__dict__)
                return None
        except Exception as e:
            logger.error("获取卡片详情失败", error=str(e), card_id=card_id)
            return None

    async def update_card(self,data: GitHubCardBase) -> Optional[GitHubCard]:

        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == data.id)
                )
                card = result.scalar_one_or_none()

                if not card:
                    logger.error("卡片不存在", card_id=data.id)
                    return None

                # 更新卡片
                update_data = data.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    setattr(card, key, value)

                await session.commit()
                await session.refresh(card)

                return GitHubCard.model_validate(card.__dict__)
        except Exception as e:
            logger.error("更新卡片失败", error=str(e), card_id=data.id)
            return None

    async def delete_card(self, card_id: str) -> bool:

        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == card_id)
                )
                card = result.scalar_one_or_none()
                if not card:
                    logger.error("卡片不存在", card_id=card_id)
                    return False

                await session.delete(card)
                await session.commit()

                return True
        except Exception as e:
            logger.error("删除卡片失败", error=str(e), card_id=card_id)
            return False

    async def update_card_interaction(self, card_id: str, interaction_type: str) -> bool:
        """更新卡片互动数据（点赞、收藏、不喜欢）
        """
        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == card_id)
                )
                card = result.scalar_one_or_none()

                if not card:
                    logger.error("卡片不存在", card_id=card_id)
                    return False

                # 更新互动计数
                if interaction_type not in ["like", "collect", "dislike"]:
                    logger.error("无效的互动类型", interaction_type=interaction_type)
                    return False

                current_value = getattr(card, interaction_type)
                setattr(card, interaction_type, current_value + 1)
                await session.commit()
                return True
        except Exception as e:
            logger.error("更新卡片互动数据失败", error=str(e), card_id=card_id, interaction_type=interaction_type)
            return False

    async def create_project(self, data: GitHubProjectCreate) -> Optional[GitHubProject]:
        """创建GitHub项目

        Args:
            data: 项目创建数据模型

        Returns:
            Optional[GitHubProject]: 创建成功返回项目数据，失败返回None
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否已存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.repository_url == data.repository_url)
                result = await session.execute(stmt)
                existing_project = result.scalar_one_or_none()

                if existing_project:
                    logger.error("创建项目失败：项目已存在", repository_url=data.repository_url)
                    return None

                # 创建新项目实例
                db_project = DBGitHubProject(
                    name=data.name,
                    repository_url=data.repository_url,
                    description_recommend=data.description_recommend,
                    description_project=data.description_project,
                    status=data.status if hasattr(data, 'status') else ProjectStatusEnum.DRAFT.value,
                    tags=data.tags if hasattr(data, 'tags') else [],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    button_color=data.button_color,
                    background_color=data.background_color,
                    image_url=data.image_url,
                    icon_url=data.icon_url,
                    local_path=data.local_path
                )

                # 添加到数据库
                session.add(db_project)
                await session.commit()
                await session.refresh(db_project)

                # 返回创建的项目
                return GitHubProject.model_validate(db_project.__dict__)

        except IntegrityError as e:
            logger.error("创建项目失败：数据库完整性错误", error=str(e))
            return None
        except Exception as e:
            logger.error("创建项目失败", error=str(e))
            return None

    async def create_cards_by_analyzer_content(self, project_id: str, content: str) -> Tuple[bool, List[GitHubCard], str]:
        """使用大模型分析内容并批量创建项目卡片

        Args:
            project_id: 项目ID
            content: 要分析的Markdown内容

        Returns:
            Tuple[bool, List[GitHubCard], str]: (成功标志, 创建的卡片列表, 错误信息)
        """
        try:
            # 检查项目是否存在
            async with self.async_session() as session:
                project = await session.execute(
                    select(GitHubProjectModel).where(GitHubProjectModel.id == project_id)
                )
                project = project.scalar_one_or_none()
                if not project:
                    logger.error("批量创建卡片失败：项目不存在", project_id=project_id)
                    return False, [], "项目不存在"

            # 初始化LangChain的ChatOpenAI客户端
            llm = ChatOpenAI(
                temperature=0.0,
                model="deepseek-reasoner",
                base_url="https://api.deepseek.com/v1",
                max_tokens=8192,
                api_key="sk-b31c5a7c8d054f2db7d038a321d4920d"
            )

            # 构建提示词
            prompt = f"""
            请将以下Markdown文档分割成多个独立的Markdown文档，每个文档应该具有一个清晰的主题。
            
            要求:
            1. 返回纯JSON格式，不要包含代码块标记
            2. 使用如下格式的JSON数组：[{{"title": "文档标题", "content": "文档内容"}}]
            3. 每个文档应该有一个逻辑上独立的内容部分，适合作为单独的项目卡片
            4. 标题应简明扼要地总结文档内容
            
            原始Markdown文档:
            ```
            {content}
            ```
            """

            # 使用LangChain调用大模型
            messages = [HumanMessage(content=prompt)]
            response = llm.invoke(messages)
            result = response.content

            # 清理结果（移除可能的代码块标记）
            code_block_match = re.search(r'```(?:json)?\s*([\s\S]*?)```', result)
            if code_block_match:
                cleaned_json = code_block_match.group(1).strip()
            else:
                cleaned_json = result.strip()

            # 解析JSON
            try:
                documents = json.loads(cleaned_json)
                created_cards = []
                
                # 创建卡片
                async with self.async_session() as session:
                    # 获取当前项目卡片数量，用于设置排序顺序
                    result = await session.execute(
                        select(func.count()).select_from(GitHubProjectCardModel)
                        .where(GitHubProjectCardModel.project_id == project_id)
                    )
                    current_count = result.scalar_one() or 0
                    
                    for i, doc in enumerate(documents):
                        # 检查文档是否包含所需字段
                        if "title" not in doc or "content" not in doc:
                            logger.warning("文档缺少所需字段", doc=doc)
                            continue
                            
                        # 创建卡片
                        card = GitHubProjectCardModel(
                            title=doc["title"],
                            content=doc["content"],
                            project_id=project_id,
                            sort_order=current_count + i,  # 设置排序顺序
                            like=0,
                            collect=0,
                            dislike=0
                        )
                        session.add(card)
                    
                    # 提交所有变更
                    await session.commit()
                    
                    # 获取刚创建的卡片
                    result = await session.execute(
                        select(GitHubProjectCardModel)
                        .where(GitHubProjectCardModel.project_id == project_id)
                        .order_by(GitHubProjectCardModel.created_at.desc())
                        .limit(len(documents))
                    )
                    cards = result.scalars().all()
                    created_cards = [GitHubCard.model_validate(card.__dict__) for card in cards]
                
                logger.info("批量创建卡片成功", project_id=project_id, card_count=len(created_cards))
                return True, created_cards, ""
                
            except json.JSONDecodeError as e:
                logger.error("JSON解析错误", error=str(e), result=cleaned_json[:200])
                return False, [], f"解析分割结果失败: {str(e)}"
                
        except Exception as e:
            logger.error("批量创建卡片失败", error=str(e), project_id=project_id)
            return False, [], f"服务器错误: {str(e)}"

    async def solve_data(self, count: int) -> Dict:
        try:
            async with self.async_session() as session:
                # 步骤1：查询状态为 'gererating' 的项目
                generating_stmt = select(DBGitHubProject).where(DBGitHubProject.status == ProjectStatusEnum.Generate.value)
                result = await session.execute(generating_stmt)
                generating_projects = result.scalars().all()

                if not generating_projects:
                    return {
                        "status": "success",
                        "message": "没有找到状态为 'gererating' 的项目",
                        "data": {
                            "generating_count": 0,
                            "updated_count": 0,
                            "updated_projects": []
                        }
                    }

                # 记录要更新的项目
                projects_to_update = []

                # 步骤2：检查每个项目的卡片数量
                project_ids = [project.id for project in generating_projects]
                card_counts_stmt = (
                    select(
                        GitHubProjectCardModel.project_id,
                        func.count(GitHubProjectCardModel.id).label('card_count')
                    )
                        .where(GitHubProjectCardModel.project_id.in_(project_ids))
                        .group_by(GitHubProjectCardModel.project_id)
                )

                result = await session.execute(card_counts_stmt)
                card_counts = {row.project_id: row.card_count for row in result}

                # 步骤3：更新符合条件的项目状态
                updated_projects = []
                for project in generating_projects:
                    card_count = card_counts.get(project.id, 0)
                    if card_count > 5:
                        project.status = ProjectStatusEnum.PUBLISHED.value
                        updated_projects.append({
                            "id": str(project.id),
                            "name": project.name,
                            "repository_url": project.repository_url,
                            "card_count": card_count,
                            "old_status": "generating",
                            "new_status": "true"
                        })

                await session.commit()

                return {
                    "status": "success",
                    "message": f"成功处理项目数据",
                    "data": {
                        "generating_count": len(generating_projects),
                        "updated_count": len(updated_projects),
                        "updated_projects": updated_projects
                    }
                }

        except Exception as e:
            logger.error(f"处理项目数据时发生错误: {str(e)}")
            return {
                "status": "error",
                "message": f"服务器错误: {str(e)}",
                "data": None
            }

    async def delete_wrong_cards_porject(self, count: int) -> Dict:
        """删除具有3-5张卡片的项目中的所有卡片

        Args:
            count: 要处理的项目数量

        Returns:
            Dict: 包含删除结果的字典
        """
        try:
            async with self.async_session() as session:
                # 首先查询具有3-5张卡片的项目
                card_count_subquery = (
                    select(
                        GitHubProjectCardModel.project_id,
                        func.count(GitHubProjectCardModel.id).label('card_count')
                    )
                        .group_by(GitHubProjectCardModel.project_id)
                        .having(and_(
                        func.count(GitHubProjectCardModel.id) >= 3,
                        func.count(GitHubProjectCardModel.id) <= 5
                    ))
                        .subquery()
                )

                # 查询符合条件的项目及其卡片
                stmt = (
                    select(DBGitHubProject, GitHubProjectCardModel)
                        .join(card_count_subquery, DBGitHubProject.id == card_count_subquery.c.project_id)
                        .join(GitHubProjectCardModel, DBGitHubProject.id == GitHubProjectCardModel.project_id)
                        .limit(count)
                )

                result = await session.execute(stmt)
                projects_and_cards = result.all()

                if not projects_and_cards:
                    return {
                        "status": "success",
                        "data": {
                            "processed_count": 0,
                            "processed_projects": []
                        },
                        "message": "没有找到符合条件的项目"
                    }

                # 整理要处理的项目和卡片
                processed_projects = {}
                for project, card in projects_and_cards:
                    if project.id not in processed_projects:
                        processed_projects[project.id] = {
                            "project": {
                                "id": str(project.id),
                                "name": project.name,
                                "repository_url": project.repository_url
                            },
                            "deleted_cards": []
                        }

                    # 记录要删除的卡片信息
                    processed_projects[project.id]["deleted_cards"].append({
                        "id": str(card.id),
                        "title": card.title
                    })

                    # 删除卡片
                    await session.delete(card)

                # 提交删除操作
                await session.commit()

                # 准备返回结果
                result_data = {
                    "processed_count": len(processed_projects),
                    "processed_projects": [
                        {
                            "project": project_data["project"],
                            "deleted_cards_count": len(project_data["deleted_cards"]),
                            "deleted_cards": project_data["deleted_cards"]
                        }
                        for project_data in processed_projects.values()
                    ]
                }

                return {
                    "status": "success",
                    "data": result_data,
                    "message": f"成功处理 {len(processed_projects)} 个项目的卡片"
                }

        except Exception as e:
            logger.error(f"删除项目卡片时发生错误: {str(e)}")
            return {
                "status": "error",
                "data": None,
                "message": f"服务器错误: {str(e)}"
            }

    async def queue_unanalyzed_projects(self, count: int) -> Dict:

        try:
            current_queue = await GitHubReadmeGenerate.get_generate_queue()
            async with self.async_session() as session:
                # 查询未分析的项目，并排除已在队列中的项目
                stmt = select(DBGitHubProject).where(
                    and_(
                        # DBGitHubProject.architecture_mermaid.is_(None),
                        DBGitHubProject.project_phase == "download_success",
                        DBGitHubProject.id.notin_(current_queue)  # 排除已在队列中的项目
                    )
                ).limit(count)
                result = await session.execute(stmt)
                unanalyzed_projects = result.scalars().all()

                queued_projects = []
                failed_projects = []

                for project in unanalyzed_projects:
                    try:
                        # 检查本地路径是否存在
                        if not project.local_path:
                            logger.warning(f"项目 {project.id} 本地路径不存在，跳过分析")
                            failed_projects.append({
                                "id": str(project.id),
                                "error": "本地路径不存在"
                            })
                            continue

                        # 添加到生成队列
                        await GitHubReadmeGenerate.add_to_generate_queue(str(project.id))
                        project.project_phase = ProjectStatusEnum.WAIT_GENERATE
                        queued_projects.append(str(project.id))
                        logger.info(f"项目 {project.id} 已添加到分析队列")

                    except Exception as e:
                        logger.error(f"处理项目 {project.id} 时发生错误: {str(e)}")
                        failed_projects.append({
                            "id": str(project.id),
                            "error": str(e)
                        })

                # 获取当前队列状态
                queue_status = await GitHubReadmeGenerate.get_generate_queue_status()
                await session.commit()
                return {
                    "status": "success",
                    "data": {
                        "queued_projects": queued_projects,
                        "failed_projects": failed_projects,
                        "queue_status": queue_status
                    },
                    "message": f"成功添加 {len(queued_projects)} 个项目到分析队列，{len(failed_projects)} 个项目处理失败"
                }

        except Exception as e:
            logger.error(f"查找未分析项目时发生错误: {str(e)}")
            return {
                "status": "error",
                "data": None,
                "message": f"服务器错误: {str(e)}"
            }

    async def batch_update_cards(self, project_id: str, cards_data: List[Dict]) -> List[GitHubCard]:
        """批量更新项目卡片（删除所有卡片并添加新卡片）

        Args:
            project_id: 项目ID
            cards_data: 卡片数据列表

        Returns:
            List[GitHubCard]: 创建的卡片列表
        """
        # 1. 删除项目下所有卡片
        ans = await self.delete_all_cards_by_project(project_id)
        if not ans:
            raise ValueError(500, "删除卡片失败 操作取消")
        # 2. 批量创建新卡片
        created_cards = []
        for card_data in cards_data:
            # 确保每个卡片数据中包含项目ID
            card_data["project_id"] = project_id
            # 创建卡片
            card = await self.create_card(GitHubCardCreate(**card_data))
            created_cards.append(card)

        return created_cards

    async def delete_all_cards_by_project(self, project_id: str) -> bool:
        """删除项目下的所有卡片

        Args:
            project_id: 项目ID

        Returns:
            bool: 是否删除成功
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否存在
                project_result = await session.execute(
                    select(GitHubProjectModel).where(GitHubProjectModel.id == project_id)
                )
                project = project_result.scalar_one_or_none()
                if not project:
                    logger.error("删除所有卡片失败：项目不存在", project_id=project_id)
                    return False

                # 删除所有关联的卡片
                delete_stmt = delete(GitHubProjectCardModel).where(
                    GitHubProjectCardModel.project_id == project_id
                )
                await session.execute(delete_stmt)
                await session.commit()

                logger.info("已删除项目下的所有卡片", project_id=project_id)
                return True
        except Exception as e:
            logger.error("删除项目下所有卡片失败", error=str(e), project_id=project_id)
            return False

    async def get_file_content(self, project_id: str, file_path: str) -> dict:
        """获取项目文件内容

        Args:
            project_id: 项目ID
            file_path: 文件相对路径

        Returns:
            dict: 包含文件内容和相关信息的字典

        Raises:
            ValueError: 参数错误或文件读取错误
            NotFoundError: 项目或文件不存在
            PermissionError: 文件路径非法
        """
        # 获取项目详情
        project = await self.get_project(project_id)
        if not project:
            raise Exception(f"未找到项目: {project_id}")

        local_path = project.local_path
        if not local_path or not os.path.exists(local_path):
            raise Exception(f"项目本地路径不存在: {local_path}")

        # 获取文件的完整路径
        full_file_path = os.path.join(local_path, file_path)

        # 检查路径合法性（防止目录遍历攻击）
        if not os.path.normpath(full_file_path).startswith(os.path.normpath(local_path)):
            raise PermissionError("非法的文件路径")

        # 检查文件是否存在
        if not os.path.exists(full_file_path):
            raise Exception(f"文件不存在: {file_path}")

        # 检查是否是目录
        if os.path.isdir(full_file_path):
            raise ValueError(f"指定路径是一个目录，不是文件: {file_path}")

        # 获取文件大小
        file_size = os.path.getsize(full_file_path)

        # 读取文件内容和编码
        content, encoding = await self._read_file_content(full_file_path)

        # 返回符合预期格式的响应
        return {
            "file_path": file_path,
            "project_id": project_id,
            "file_size": file_size,
            "content": content,
            "encoding": encoding
        }

    async def _read_file_content(self, file_path: str) -> tuple[str, str]:
        """读取文件内容及其编码

        Args:
            file_path: 文件的完整路径

        Returns:
            tuple: (文件内容, 编码)

        Raises:
            ValueError: 文件读取失败
        """
        try:
            # 文件大小限制（例如10MB）
            max_size = 10 * 1024 * 1024
            if os.path.getsize(file_path) > max_size:
                return f"文件过大，超过了{max_size / (1024 * 1024)}MB的限制，无法显示", "unknown"

            # 二进制文件类型列表
            binary_extensions = [
                '.zip', '.rar', '.tar', '.gz', '.bz2', '.7z',  # 压缩文件
                '.exe', '.dll', '.so', '.dylib',  # 可执行文件
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',  # 图像
                '.mp3', '.wav', '.ogg', '.flac',  # 音频
                '.mp4', '.avi', '.mov', '.mkv',  # 视频
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',  # 文档
                '.o', '.obj', '.class', '.pyc',  # 编译文件
            ]

            # 检查是否是二进制文件
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in binary_extensions:
                return f"[二进制文件 {file_ext}] - 无法显示内容", "binary"

            # 尝试以文本方式读取文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read(), "utf-8"
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试其他编码
                for encoding in ['gbk', 'latin-1', 'cp1252']:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            return f.read(), encoding
                    except UnicodeDecodeError:
                        continue

                # 如果所有编码都失败，将其视为二进制文件
                return "[无法解码的文件] - 可能是二进制文件或使用了不支持的编码", "unknown"

        except Exception as e:
            logger.error(f"读取文件内容失败", file_path=file_path, error=str(e))
            raise ValueError(f"读取文件内容失败: {str(e)}")