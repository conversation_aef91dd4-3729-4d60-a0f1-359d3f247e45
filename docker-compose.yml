services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: gugu-apex-postgres
    environment:
      POSTGRES_DB: gugu_apex
      POSTGRES_USER: gugu_apex
      POSTGRES_PASSWORD: gugu_apex_pass
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gugu_apex"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - gugu-network
      
  # Elasticsearch服务
  elasticsearch:
    build: ./elasticsearch/dockerfile
    container_name: gugu-apex-elasticsearch
    environment:
      # 节点名称
      - node.name=elasticsearch
      # 集群名称
      - cluster.name=gugu-apex-cluster
      # 单节点模式
      - discovery.type=single-node
      # 网络设置
      - network.host=0.0.0.0
      # 内存锁定设置
      - bootstrap.memory_lock=true
      # JVM堆内存设置
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      # 安全设置（开发环境，可以设置为false）
      - xpack.security.enabled=false
      # 监控设置
      - xpack.monitoring.collection.enabled=true
      # 数据路径
      - path.data=/usr/share/elasticsearch/data
      # 日志路径
      - path.logs=/usr/share/elasticsearch/logs
      # HTTP端口
      - http.port=9200
    volumes:
      - ./elasticsearch/data:/usr/share/elasticsearch/data
      - ./elasticsearch/logs:/usr/share/elasticsearch/logs
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "9200:9200"
      - "9300:9300"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl -s -f http://localhost:9200"]
      interval: 45s
      timeout: 15s
      retries: 3
      start_period: 120s
    networks:
      - gugu-network
      
  # Logstash服务
  logstash:
    build:
      context: ./logstash/dockerfile
      dockerfile: Dockerfile
    container_name: gugu-apex-logstash
    environment:
      # 设置JVM选项
      - LS_JAVA_OPTS=-Xms512m -Xmx512m
      # 设置管道自动重载
      - config.reload.automatic=true
      # 重载间隔
      - config.reload.interval=30s
      # 设置监控
      - monitoring.enabled=true
      # 设置Elasticsearch主机
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      # 挂载配置文件
      - ./logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
      - ./logstash/config/pipelines.yml:/usr/share/logstash/config/pipelines.yml
      # 挂载管道配置
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      # 挂载最后更新时间文件
      - ./logstash/data:/usr/share/logstash/data
      # 挂载模板
      - ./logstash/templates:/usr/share/logstash/templates
      # 挂载JDBC驱动
      - ./logstash/config/jdbc-connector/postgresql-42.7.3.jar:/usr/share/logstash/logstash-core/lib/jars/postgresql-42.7.3.jar
      # 宿主机时间挂载
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "5044:5044"
      - "9600:9600"  # Logstash API
    depends_on:
      elasticsearch:
        condition: service_healthy
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "-s", "http://localhost:9600"]
      interval: 45s
      timeout: 15s
      retries: 3
      start_period: 90s
    networks:
      - gugu-network
      
  # Kibana服务
  kibana:
    image: docker.elastic.co/kibana/kibana:9.0.1
    container_name: gugu-apex-kibana
    environment:
      # 节点名称
      - NODE_NAME=kibana
      # Elasticsearch连接
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      # Kibana配置文件路径
      - KIBANA_CONFIG_PATH=/usr/share/kibana/config/kibana.yml
      # 服务器设置
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=5601
      # 服务器名称
      - SERVER_NAME=gugu-apex-kibana
      # 监控设置
      - MONITORING_UI_CONTAINER_ELASTICSEARCH_ENABLED=true
      # 启用日志
      - LOGGING_VERBOSE=false
    volumes:
      # 挂载自定义配置文件
      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
      # Kibana数据持久化
      - ./data/kibana:/usr/share/kibana/data
      # 宿主机时间挂载
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "-s", "http://localhost:5601"]
      interval: 45s
      timeout: 15s
      retries: 3
      start_period: 90s
    networks:
      - gugu-network

  # MongoDB数据库
  mongodb:
    image: mongo:7
    container_name: gugu-apex-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: gugu_apex
      MONGO_INITDB_ROOT_PASSWORD: gugu_apex_pass
      MONGO_INITDB_DATABASE: gugu_apex
    volumes:
      - ./data/mongodb:/data/db
      - ./mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
      # 宿主机时间挂载
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "27017:27017"
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')", "--quiet"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - gugu-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: gugu-apex-redis
    command: >
      redis-server
      --requirepass gugu_apex_pass
      --appendonly yes
      --appendfsync everysec
      --save 900 1
      --save 300 10
      --save 60 10000
      --dir /data
    volumes:
      - ./data/redis:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      # 宿主机时间挂载
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "gugu_apex_pass", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - gugu-network

  # 后端服务
  backend:

    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: gugu-apex-backend
    environment:
      # 数据库配置
      - POSTGRESQL_HOST=postgres
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DB=gugu_apex
      - POSTGRESQL_USER=gugu_apex
      - POSTGRESQL_PASSWORD=gugu_apex_pass
      # MongoDB配置
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_DB=gugu_apex
      - MONGODB_USER=gugu_apex
      - MONGODB_PASSWORD=gugu_apex_pass
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=gugu_apex_pass
      - REDIS_DB=0
      # Elasticsearch配置
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      # 日志配置
      - LEVEL=INFO
      - DIRECTORY=/app/logs
      - MAX_FILE_SIZE=104857600
      - BACKUP_COUNT=30
      - ERROR_BACKUP_COUNT=60
      # JWT配置
      - JWT_SECRET=TXpgxm_iZXjNBMAUZ4XI34s_5Me0FFdA8Ub8kDdNFlk
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=11520
      - JWT_REFRESH_TOKEN_EXPIRE_DAYS=30
    volumes:
      - ./backend/logs:/app/logs
      - ./data/uploads:/data/uploads  # 处理图片 将宿主机的./uploads目录挂载到容器的/data/uploads
      - ./data/projects:/data/projects  # 挂载下载的github_project
      - ./data/readme:/data/readme  # 挂载生成的的readme
      - ./backend/static:/app/static  # 添加这一行，将本地的static目录挂载到容器中
      - ./.cache:/app/.cache # 挂载缓存目录
      # 宿主机时间挂载
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    networks:
      - gugu-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: gugu-apex-frontend
    environment:
      - BACKEND_API_URL=http://backend:8000
    volumes:
      # 宿主机时间挂载
      - /etc/localtime:/etc/localtime:ro
      # - /etc/timezone:/etc/timezone:ro
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "-s", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - gugu-network

# volumes1:
#  postgres_data:
#  mongodb_data:
#  redis_data:
#  elasticsearch_data:
#  elasticsearch_logs:
#  kibana_data:
#  uploads:
#  logstash_data:

networks:
  gugu-network:
    driver: bridge
