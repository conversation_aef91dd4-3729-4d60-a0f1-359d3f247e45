<template>
  <el-menu
    :default-openeds="['4']"
    class="el-menu-vertical"
    :collapse="isCollapse"
    @open="handleOpen"
    @close="handleClose">
    <el-menu-item index="title">
      <template #title>
        <img src="@/assets/images/profile.png" alt="" style="width: 30px" />
        <div class="info">
          <div class="name" :title="userStore.userInfo.username">
            {{ userStore.userInfo.username }}
          </div>
          <div
            class="description"
            :title="userStore.userInfo.email || userStore.userInfo.phone">
            {{ userStore.userInfo.email || userStore.userInfo.phone }}
          </div>
        </div>
      </template>
    </el-menu-item>
    <template v-for="(item, index) in menuList" :key="index">
      <el-sub-menu :index="index.toString()" v-if="item.children">
        <template #title>
          <img
            :src="getImageUrl(item.icon)"
            style="
              width: 14px;
              height: 14px;
              margin-right: 16px;
              margin-left: 20px;
            " />
          <span>{{ item.label }}</span>
        </template>
        <el-menu-item
          :class="{
            'is-active': router.currentRoute.value.path === childItem.path,
          }"
          v-for="(childItem, childIndex) in item.children"
          :key="`${index}-${childIndex}`"
          :index="`${index}-${childIndex}`">
          <div style="display: flex" @click="skip(childItem.path)">
            <div style="width: 4px; height: 4px; margin-right: 16px"></div>
            <div>{{ childItem.label }}</div>
          </div>
        </el-menu-item>
      </el-sub-menu>
      <el-menu-item
        :class="{
          'is-active': router.currentRoute.value.path === item.path,
        }"
        :index="index.toString()"
        v-else
        @click="skip(item.path)">
        <img
          :src="getImageUrl(item.icon)"
          style="width: 14px; height: 14px; margin-right: 16px" />
        <template #title>
          {{ item.label }}
        </template>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup>
import useUserStore from "@/store/modules/user";
import { watch } from "vue";
const userStore = useUserStore();
const router = useRouter();
const props = defineProps({
  project: {
    type: Object,
    default: () => ({}),
  },
  width: {
    type: [Number, String],
    default: "100%",
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
});

// const isCollapse = ref(true);
let menuList = ref([
  {
    label: "个人资料",
    icon: "icon01",
    path: "/personal/homepage",
  },
  {
    label: "任务列表",
    icon: "icon20",
    path: "/personal/tasklist",
  },
  {
    label: "历史记录",
    icon: "icon21",
    path: "/personal/historyrecord",
  },
  {
    label: "收藏夹",
    icon: "icon22",
    path: "/personal/favorite",
  },
  {
    label: "消息中心",
    icon: "icon24",
    path: "/personal/message",
    children: [
      {
        label: "系统消息",
        icon: "icon24",
        path: "/personal/message",
      },
      {
        label: "评论回复",
        icon: "icon24",
        path: "",
      },
    ],
  },
]);
let currentMenu = ref();
// watch(
//   () => router.currentRoute.value.path,
//   (newVal) => {
//     console.log(
//       router.currentRoute.value.path,
//       "router.currentRoute.value.pathrouter.currentRoute.value.path"
//     );

//     currentMenu.value = menuList.value.find((item) => item.path === newVal);
//   },
//   { immediate: true }
// );

// 处理图标路径
const getImageUrl = (iconName) => {
  try {
    // 使用动态导入确保图片被正确打包
    return new URL(`../../../../assets/images/${iconName}.png`, import.meta.url)
      .href;
  } catch (error) {
    console.error("图片加载失败:", error);
    // 返回一个默认图标路径作为备选
    return new URL("../../../../assets/images/icon01.png", import.meta.url)
      .href;
  }
};
const skip = (path) => {
  router.push(path);
};

const isCollapse = ref(false);
const handleOpen = (key, keyPath) => {
  console.log(key, keyPath);
};
const handleClose = (key, keyPath) => {
  console.log(key, keyPath);
};
</script>

<style scoped lang="scss">
.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}
.el-menu-vertical {
  width: 200px;
  height: 100%;
  background: #f1f1f1;
  --el-menu-item-height: 30px;
  --el-menu-hover-bg-color: rgba(0, 0, 0, 0.06);

  .info {
    margin-left: 10px;
    width: 120px;
    .name {
      line-height: normal;
      font-size: 14px;
      color: #535353;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .description {
      line-height: normal;
      font-size: 12px;
      color: #b0b0b0;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }
  ::v-deep(.el-menu-item) {
    width: 95%;
    height: 30px;
    font-size: 14px;
    color: #4c6d7d;
    margin: 0 auto;
    margin-top: 10px;
    border-radius: 5px;
  }

  .is-active {
    background-color: rgba(0, 0, 0, 0.06);
  }
  ::v-deep(.el-menu) {
    background: #f1f1f1;
  }
  ::v-deep(.el-sub-menu) {
    margin-top: 10px;

    .el-sub-menu__title {
      padding: 0 20px;
      width: 95%;
      margin: 0 auto;

      box-sizing: border-box;
      font-size: 14px;
      color: #4c6d7d;
      border-radius: 5px;
    }

    .el-menu-item {
      line-height: normal;
    }
  }

  ::v-deep(.el-sub-menu__icon-arrow) {
    width: auto;
  }
}
</style>
