<template>
  <div class="container">
    <el-button type="primary" @click="handleTests">点击</el-button>
    <div class="tests" :class="{ hidden: !visi }">
      <div class="test">jasjjda</div>
    </div>
    <div class="content">
      <LeftMenu></LeftMenu>
      <div class="right" ref="mainRef">
        <div class="right-top">
          <el-tabs v-model="activeTab" class="demo-tabs">
            <el-tab-pane
              v-for="item in tabList"
              :label="item.label"
              :name="item.name">
              <div>
                <div class="all-read" @click="handleAllRead">全部已读</div>
                <div
                  class="msg"
                  :style="{
                    color: item.is_read || allRead ? '#A3A3A3' : '#f09170',
                  }"
                  v-for="(item, index) in sysMsgList"
                  :key="item.id"
                  @click="handleMsg(item.is_read, item.id, index)">
                  <div
                    :style="{
                      whiteSpace: item.is_read || allRead ? 'unset' : 'nowrap',
                      textOverflow:
                        item.is_read || allRead ? 'unset' : 'ellipsis',
                    }">
                    {{ item.message }}
                  </div>
                  <div
                    style="width: 150px; margin-left: 30px; text-align: right">
                    {{ dayjs(item.updated_at).format("YYYY-MM-DD HH:mm") }}
                  </div>
                </div>
              </div>
              <div class="demo-pagination-block">
                <el-pagination
                  v-model:current-page="page"
                  v-model:page-size="pageSize"
                  :size="size"
                  :disabled="disabled"
                  :background="background"
                  layout="total, prev, pager, next, jumper"
                  :total="total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  style="margin-top: 10px" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <HomeFooter style="position: fixed; bottom: 0"></HomeFooter>
  </div>
</template>

<script setup>
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import LeftMenu from "@/views/broadcast/personal/components/LeftMenu.vue";
import dayjs from "dayjs";
import { sysMsg, sysMsgRead, sysMsgReadAll } from "@/api/broadcast";
import useUserStore from "@/store/modules/user";
const size = ref("default");
const background = ref(false);
const disabled = ref(false);

const userStore = useUserStore();
let activeTab = ref("1");
const sysMsgList = ref([]);
let tabList = ref([{ label: "消息中心", name: "1" }]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(12);
const handleSizeChange = (val) => {
  page.value = val;
  getSysMsgList();
};
const handleCurrentChange = (val) => {
  page.value = val;
  getSysMsgList();
};
onMounted(() => {
  getSysMsgList();
});
const getSysMsgList = () => {
  sysMsg({
    page: page.value,
    page_size: pageSize.value,
  }).then((res) => {
    if (res.code == 200) {
      if (res.code == 200) {
        userStore.unread_count = res.data.unread_count;
        sysMsgList.value = res.data.logs;
        total.value = res.data.total;
      }
    }
  });
};
const handleMsg = (read, id, index) => {
  if (read) {
    return;
  }
  let count = userStore.unread_count;
  userStore.unread_count -= 1;
  sysMsgList.value[index].is_read = true;
  sysMsgRead({
    log_ids: [id],
  }).then((res) => {
    if (res.code !== 200) {
      sysMsgList.value[index].is_read = false;
      userStore.unread_count = count;
    }
  });
};

const allRead = ref(false);
const handleAllRead = () => {
  let count = userStore.unread_count;
  userStore.unread_count = 0;
  allRead.value = true;
  sysMsgReadAll().then((res) => {
    if (res.code !== 200) {
      userStore.unread_count = count;
      allRead.value = false;
    }
  });
};
let visi = ref(true);
const handleTests = () => {
  visi.value = !visi.value;
};
</script>
<style scoped lang="scss">
.container {
  padding-top: 12px;
  width: 100vw;
}
.content {
  position: relative;
  width: 960px;
  height: calc(100vh - var(--navbar-height) - 12px - 40px);
  overflow-y: auto;
  margin: 0 auto;
  display: flex;
  :deep(.el-tabs__content) {
    overflow: inherit;
  }
  .right {
    width: calc(960px - 200px);
    overflow: auto;
    .right-top {
      padding: 0 20px;

      .all-read {
        position: absolute;
        top: -43px;
        right: 5px;
        z-index: 99;
        font-size: 14px;
        color: #4c6d7d;
        cursor: pointer;
      }

      .msg {
        padding: 12px 21px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #c4c4c4;
        font-size: 14px;
        color: #f09170;
        cursor: pointer;

        & > div:first-child {
          flex: 1;
          white-space: nowrap; /* 禁止换行 */
          word-break: break-all;
          overflow: hidden; /* 隐藏溢出内容 */
          text-overflow: ellipsis; /* 显示省略号 */
        }
      }
    }
  }
}
.tests {
  background-color: aquamarine;
  overflow: hidden;
  transition: all 1s;
  // height: 100px;
  display: flex;
  flex-direction: column;
  .test {
    flex: 1;
  }
  &.hidden {
    .test {
      flex: 0;
    }
  }
}
</style>
